from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QScrollArea, QGroupBox, QFrame, QSizePolicy,
                             QFileDialog, QListWidget, QMessageBox, QAbstractItemView,
                             QProgressBar, QTabWidget)
from PyQt5.QtCore import Qt
from .movie_destinations import MovieDestinationWidget, create_new_movie_destination, get_movie_destinations
from settings.settings_manager import load_settings, save_settings
from .movie_handler import process_gui_movies, get_movies_folder
import logging
from pathlib import Path
import re
import shutil
import uuid

# Configure logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

class MovieListWidget(QListWidget):
    """Custom QListWidget with drag-and-drop functionality for movie files."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setDragDropMode(QAbstractItemView.DropOnly)
        self.setAlternatingRowColors(True)
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.main_window = None
        self.file_paths = []
        self.is_dragging = False
        self.movie_extensions = {'.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.mpg', '.mpeg', '.3gp'}
        # Set dark style for all states
        self.setStyleSheet("""
            QListWidget {
                background-color: #181a20;
                color: #fff;
                border: 2px dashed #888;
                border-radius: 5px;
                padding: 10px;
            }
            QListWidget::item {
                background: #181a20;
                color: #fff;
            }
            QListWidget::item:selected {
                background: #2c313c;
                color: #fff;
            }
            QListWidget::item:hover {
                background: #252a34;
                color: #fff;
            }
        """)

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            self.is_dragging = True
            self.setStyleSheet("""
                QListWidget {
                    background-color: #181a20;
                    color: #fff;
                    border: 2px dashed #0078d7;
                    border-radius: 5px;
                    padding: 10px;
                }
                QListWidget::item {
                    background: #181a20;
                    color: #fff;
                }
                QListWidget::item:selected {
                    background: #2c313c;
                    color: #fff;
                }
                QListWidget::item:hover {
                    background: #252a34;
                    color: #fff;
                }
            """)
            event.accept()
        else:
            event.ignore()

    def dragLeaveEvent(self, event):
        self.is_dragging = False
        self.setStyleSheet("""
            QListWidget {
                background-color: #181a20;
                color: #fff;
                border: 2px dashed #888;
                border-radius: 5px;
                padding: 10px;
            }
            QListWidget::item {
                background: #181a20;
                color: #fff;
            }
            QListWidget::item:selected {
                background: #2c313c;
                color: #fff;
            }
            QListWidget::item:hover {
                background: #252a34;
                color: #fff;
            }
        """)
        super().dragLeaveEvent(event)

    def dragMoveEvent(self, event):
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()

    def dropEvent(self, event):
        self.is_dragging = False
        self.setStyleSheet("""
            QListWidget {
                background-color: #181a20;
                color: #fff;
                border: 2px dashed #888;
                border-radius: 5px;
                padding: 10px;
            }
            QListWidget::item {
                background: #181a20;
                color: #fff;
            }
            QListWidget::item:selected {
                background: #2c313c;
                color: #fff;
            }
            QListWidget::item:hover {
                background: #252a34;
                color: #fff;
            }
        """)

        if not event.mimeData().hasUrls():
            event.ignore()
            return

        event.accept()
        try:
            file_paths = []
            skipped_files = []

            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                path = Path(file_path)
                if path.is_file():
                    if path.suffix.lower() in self.movie_extensions:
                        if str(path) not in self.file_paths:  # Prevent duplicates
                            file_paths.append(str(path))
                            self.addItem(path.name)
                            if self.main_window:
                                self.main_window.log_action(f"Added movie file: {path.name}")
                    else:
                        skipped_files.append(path.name)

            # Add all valid files at once
            if file_paths:
                self.file_paths.extend(file_paths)
                if self.main_window:
                    self.main_window.log_action(f"Successfully added {len(file_paths)} movie files")

            # Report skipped files
            if skipped_files and self.main_window:
                self.main_window.log_action(f"Skipped {len(skipped_files)} non-movie files")

        except Exception as e:
            if self.main_window:
                self.main_window.log_action(f"Error adding movie files: {e}")

    def add_files(self, file_paths):
        """Add files to the list widget with error handling"""
        try:
            added_count = 0
            skipped_count = 0

            for file_path in file_paths:
                path = Path(file_path)
                if path.is_file():
                    if path.suffix.lower() in self.movie_extensions:
                        if str(path) not in self.file_paths:  # Prevent duplicates
                            self.file_paths.append(str(path))
                            self.addItem(path.name)
                            added_count += 1
                    else:
                        skipped_count += 1

            if self.main_window:
                if added_count > 0:
                    self.main_window.log_action(f"Successfully added {added_count} movie files")
                if skipped_count > 0:
                    self.main_window.log_action(f"Skipped {skipped_count} non-video files")

        except Exception as e:
            if self.main_window:
                self.main_window.log_action(f"Error adding movie files: {e}")

    def clear(self):
        """Override clear to also clear file paths"""
        super().clear()
        self.file_paths = []

    def set_main_window(self, main_window):
        self.main_window = main_window

class MovieTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.settings = load_settings()
        self.folder_widgets = []
        self.file_list = MovieListWidget()  # Use our custom widget
        self.file_list.set_main_window(parent)  # Set the main window reference
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)

        # Initialize the Plex-style movie library
        from media_library.plex_style_library import PlexStyleMovieLibrary
        self.plex_library = PlexStyleMovieLibrary(self)

        # Setup UI and load destinations
        self.setup_ui()
        self.load_destinations()

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        self.setLayout(layout)

        # Header section
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #23272e;
                border-radius: 5px;
                padding: 10px;
            }
            QLabel {
                color: #f1f1f1;
                font-size: 14px;
            }
        """)
        header_layout = QVBoxLayout()
        header_frame.setLayout(header_layout)

        title_label = QLabel("Movie Management")
        title_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #fff;")
        header_layout.addWidget(title_label)

        desc_label = QLabel("Browse, search, and sort your movie collection.")
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #e0e0e0;")
        header_layout.addWidget(desc_label)

        layout.addWidget(header_frame)

        # Create tabs for different movie functions
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: transparent;
            }
            QTabBar::tab {
                background-color: #23272e;
                color: #f1f1f1;
                padding: 8px 16px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #0078d7;
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background-color: #2c313c;
            }
        """)

        # Tab 1: Plex-style Movie Library
        library_tab = QWidget()
        library_layout = QVBoxLayout(library_tab)
        library_layout.setContentsMargins(0, 0, 0, 0)
        library_layout.addWidget(self.plex_library)
        tabs.addTab(library_tab, "Movie Library")

        # Tab 2: Movie Sorting
        sorting_tab = QWidget()
        sorting_layout = QVBoxLayout(sorting_tab)
        sorting_layout.setContentsMargins(0, 0, 0, 0)

        # Content section (destinations and file list)
        content_layout = QHBoxLayout()
        content_layout.setSpacing(15)

        # Left side - Destinations
        destinations_group = QGroupBox("Movie Sort Destinations")
        destinations_group.setStyleSheet("""
            QGroupBox {
                background-color: #23272e;
                border-radius: 5px;
                padding: 10px;
                color: #fff;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
            }
        """)
        destinations_layout = QVBoxLayout()
        destinations_group.setLayout(destinations_layout)
        destinations_group.setMinimumWidth(400)
        destinations_group.setMaximumWidth(500)

        # Create scroll area for destinations
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background: #2c313c;
                width: 10px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #555;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)

        # Create scroll content widget
        self.scroll_content = QWidget()
        self.destinations_layout = QVBoxLayout()
        self.destinations_layout.setSpacing(10)
        self.destinations_layout.setContentsMargins(0, 0, 0, 0)
        self.scroll_content.setLayout(self.destinations_layout)
        scroll_area.setWidget(self.scroll_content)
        destinations_layout.addWidget(scroll_area)

        # Add new destination and save buttons
        button_layout = QHBoxLayout()
        add_button = QPushButton("Add New Movie Destination")
        add_button.clicked.connect(self.add_new_destination)
        button_layout.addWidget(add_button)
        save_button = QPushButton("Save Destinations")
        save_button.clicked.connect(self.save_destinations)
        button_layout.addWidget(save_button)
        destinations_layout.addLayout(button_layout)

        content_layout.addWidget(destinations_group)

        # Right side - File list and controls
        right_layout = QVBoxLayout()
        right_frame = QFrame()
        right_frame.setStyleSheet("""
            QFrame {
                background-color: #181a20;
                border: 2px solid #444;
                border-radius: 8px;
            }
        """)
        right_frame_layout = QVBoxLayout()
        right_frame.setLayout(right_frame_layout)

        # Add drop area label
        drop_label = QLabel("Drag and drop movie files here or use the buttons below")
        drop_label.setAlignment(Qt.AlignCenter)
        drop_label.setStyleSheet("color: #aaa; font-size: 14px;")
        right_frame_layout.addWidget(drop_label)

        # Add file list
        right_frame_layout.addWidget(self.file_list)

        # Add progress bar
        right_frame_layout.addWidget(self.progress_bar)

        # Add buttons
        buttons_layout = QHBoxLayout()
        add_files_button = QPushButton("Add Movies")
        add_files_button.clicked.connect(self.add_files)
        add_files_button.setStyleSheet("""
            QPushButton {
                background-color: #0078d7;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0086f0;
            }
            QPushButton:pressed {
                background-color: #006ac1;
            }
        """)
        buttons_layout.addWidget(add_files_button)

        sort_button = QPushButton("Sort Movies")
        sort_button.clicked.connect(self.sort_files)
        sort_button.setStyleSheet("""
            QPushButton {
                background-color: #107c10;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #13a513;
            }
            QPushButton:pressed {
                background-color: #0e6e0e;
            }
        """)
        buttons_layout.addWidget(sort_button)

        clear_button = QPushButton("Clear List")
        clear_button.clicked.connect(self.clear_list)
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #d83b01;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ea4a10;
            }
            QPushButton:pressed {
                background-color: #c23500;
            }
        """)
        buttons_layout.addWidget(clear_button)

        right_frame_layout.addLayout(buttons_layout)
        right_layout.addWidget(right_frame)
        content_layout.addLayout(right_layout)
        sorting_layout.addLayout(content_layout)

        # Add the sorting tab to the tabs
        tabs.addTab(sorting_tab, "Movie Sorting")

        # Add tabs to main layout
        layout.addWidget(tabs)

    def load_destinations(self):
        # Clear existing widgets
        for widget in self.folder_widgets:
            widget.deleteLater()
        self.folder_widgets.clear()

        # Load destinations from settings
        destinations = get_movie_destinations()
        for dest in destinations:
            widget = MovieDestinationWidget(dest, self.settings)
            self.folder_widgets.append(widget)
            self.destinations_layout.addWidget(widget)

        # Dynamically set the minimum height of the scroll content
        widget_height = 90  # Must match MovieDestinationWidget fixed height
        spacing = self.destinations_layout.spacing()
        total_height = len(self.folder_widgets) * (widget_height + spacing)
        self.scroll_content.setMinimumHeight(total_height)
        # Always scroll to the top after loading
        parent_scroll = self.scroll_content.parent()
        if hasattr(parent_scroll, 'verticalScrollBar'):
            parent_scroll.verticalScrollBar().setValue(0)

    def get_next_number(self):
        """Get the next available number for a new destination"""
        numbers = [w.folder_data.get('number', 0) for w in self.folder_widgets]
        if not numbers:
            return 1
        return max(numbers) + 1

    def add_new_destination(self):
        number = self.get_next_number()
        new_dest = create_new_movie_destination()
        new_dest["number"] = number
        widget = MovieDestinationWidget(new_dest, self.settings)
        self.folder_widgets.append(widget)
        self.destinations_layout.addWidget(widget)
        # Update scroll content min height
        widget_height = 90
        spacing = self.destinations_layout.spacing()
        total_height = len(self.folder_widgets) * (widget_height + spacing)
        self.scroll_content.setMinimumHeight(total_height)
        widget.save_changes()
        # Scroll to the top after adding
        parent_scroll = self.scroll_content.parent()
        if hasattr(parent_scroll, 'verticalScrollBar'):
            parent_scroll.verticalScrollBar().setValue(0)

    def save_destinations(self):
        # Save the current list of destinations to settings
        custom_folders = []
        for widget in self.folder_widgets:
            data = widget.folder_data.copy()
            if hasattr(widget, 'number'):
                data['number'] = widget.number
            elif 'number' not in data:
                data['number'] = 0
            custom_folders.append(data)

        # Update settings with these destinations
        settings = load_settings()
        # Keep non-movie destinations
        other_folders = [f for f in settings.get('custom_folders', [])
                        if f.get('sort_type') != 'Movies']
        # Add movie destinations
        settings['custom_folders'] = other_folders + custom_folders
        save_settings(settings)

        # Show confirmation
        QMessageBox.information(self, "Saved", "Movie destinations saved successfully.")

    def add_files(self):
        """Open a file dialog to select movie files to sort."""
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("Video Files (*.mp4 *.mkv *.avi *.mov *.wmv *.flv *.webm *.m4v *.mpg *.mpeg *.3gp)")

        if file_dialog.exec_():
            selected_files = file_dialog.selectedFiles()
            self.file_list.add_files(selected_files)

    def clear_list(self):
        """Clear the file list."""
        self.file_list.clear()

    def update_progress(self, current, total, message=""):
        """Update the progress bar."""
        percent = int((current / total) * 100) if total > 0 else 0
        self.progress_bar.setValue(percent)

    def sort_files(self):
        """Process the files in the list using the movie_handler.py process_gui_movies function."""
        if not self.file_list.file_paths:
            QMessageBox.information(self, "No Files", "No files to sort. Please add files first.")
            return

        try:
            # Reset progress bar
            self.progress_bar.setValue(0)

            # Process the files
            total, sorted_count, unsorted_count = process_gui_movies(
                self.file_list.file_paths,
                progress_callback=self.update_progress
            )

            # Clear the list
            self.file_list.clear()

            # Show results
            QMessageBox.information(
                self,
                "Sorting Complete",
                f"Movies sorted successfully.\nTotal: {total}\nSorted: {sorted_count}\nUnsorted: {unsorted_count}"
            )

        except Exception as e:
            logger.error(f"Error sorting movies: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error sorting movies: {str(e)}")
