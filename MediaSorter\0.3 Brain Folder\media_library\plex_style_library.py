from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QLineEdit, QScrollArea, QFrame,
    QGridLayout, QSizePolicy, QFileDialog, QMessageBox,
    QComboBox, QSpacerItem, QDialog, QTextEdit
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QPixmap, QFont, QColor, QPalette, QIcon
from pathlib import Path
import os
import json
import shutil
import re
from .movie_details import MovieDetailsDialog
from .movie_player import MoviePlayer
from .player_launcher import PlayerLauncher

class MoviePoster(QFrame):
    """A movie poster widget with hover effects."""

    clicked = pyqtSignal(dict)  # Signal emitted when poster is clicked

    def __init__(self, movie_data, parent=None):
        super().__init__(parent)
        self.movie_data = movie_data
        self.setup_ui()

    def setup_ui(self):
        """Set up the poster UI."""
        self.setFixedSize(180, 270)
        self.setCursor(Qt.PointingHandCursor)

        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 5)
        layout.setSpacing(5)

        # Poster image
        self.poster_label = QLabel()
        self.poster_label.setFixedSize(180, 240)
        self.poster_label.setScaledContents(True)
        self.poster_label.setStyleSheet("""
            QLabel {
                background-color: #2d2d2d;
                border-radius: 4px;
            }
        """)

        # Load poster image if available
        poster_path = self.movie_data.get('poster_path')
        if poster_path and os.path.exists(poster_path):
            pixmap = QPixmap(poster_path)
            self.poster_label.setPixmap(pixmap)
        else:
            # Use placeholder image
            self.poster_label.setText("No Image")
            self.poster_label.setAlignment(Qt.AlignCenter)

        layout.addWidget(self.poster_label)

        # Title label
        title = self.movie_data.get('title', 'Unknown')
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-weight: bold;
            }
        """)
        layout.addWidget(title_label)

        # Set frame style
        self.setStyleSheet("""
            MoviePoster {
                background-color: transparent;
                border-radius: 4px;
            }
            MoviePoster:hover {
                background-color: #3d3d3d;
                transform: scale(1.05);
            }
        """)

    def mousePressEvent(self, event):
        """Handle mouse press events."""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.movie_data)
        super().mousePressEvent(event)

class CategoryHeader(QWidget):
    """A header for a category of movies."""

    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.title = title
        self.setup_ui()

    def setup_ui(self):
        """Set up the header UI."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 10, 0, 5)

        # Title label
        title_label = QLabel(self.title)
        title_label.setFont(QFont('Arial', 18, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        layout.addWidget(title_label)

        # Add spacer
        layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        # View All button
        view_all = QPushButton("View All")
        view_all.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #0078d7;
                border: none;
                font-weight: bold;
            }
            QPushButton:hover {
                color: #1e90ff;
            }
        """)
        layout.addWidget(view_all)

class PlexStyleMovieLibrary(QWidget):
    """A Plex-style movie library widget."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.movie_player = None
        self.movies_by_category = {}
        self.all_movies = []
        self.setup_ui()

    def find_mediaSorter_root(self):
        """Find the MediaSorter root folder regardless of where it's located."""
        # Start from the current file's directory
        current_path = Path(os.path.abspath(__file__))
        root_folder = current_path.parent

        # Go up the directory tree until we find MediaSorter or hit the root
        while root_folder.name != "MediaSorter" and str(root_folder) != root_folder.root:
            root_folder = root_folder.parent

        # If we couldn't find MediaSorter, use the parent of the current directory
        if str(root_folder) == root_folder.root:
            root_folder = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        return root_folder

    def setup_ui(self):
        """Set up the library UI."""
        # Set dark theme
        self.setStyleSheet("""
            QWidget {
                background-color: #1a1a1a;
                color: white;
            }
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QLineEdit {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #3d3d3d;
                border-radius: 4px;
                padding: 8px;
            }
            QPushButton {
                background-color: #0078d7;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1e90ff;
            }
            QComboBox {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #3d3d3d;
                border-radius: 4px;
                padding: 8px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox QAbstractItemView {
                background-color: #2d2d2d;
                color: white;
                selection-background-color: #0078d7;
            }
        """)

        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)

        # Header with search and controls
        header_layout = QHBoxLayout()

        # Search bar
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search movies...")
        self.search_input.textChanged.connect(self.search_movies)
        self.search_input.setMinimumWidth(300)
        header_layout.addWidget(self.search_input)

        # Filter dropdown
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["All", "Recently Added", "Movies", "TV Shows"])
        self.filter_combo.currentTextChanged.connect(self.apply_filter)
        header_layout.addWidget(self.filter_combo)

        # Add folder button
        add_folder_btn = QPushButton("Add Folder")
        add_folder_btn.clicked.connect(self.add_folder)
        header_layout.addWidget(add_folder_btn)

        # Scan library button
        scan_btn = QPushButton("Scan Library")
        scan_btn.clicked.connect(self.scan_library)
        header_layout.addWidget(scan_btn)

        # Refresh metadata button
        refresh_btn = QPushButton("Refresh Metadata")
        refresh_btn.clicked.connect(self.refresh_metadata)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d7;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1e90ff;
            }
        """)
        header_layout.addWidget(refresh_btn)

        layout.addLayout(header_layout)

        # Create scroll area for content
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Content widget
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(20)

        scroll.setWidget(self.content_widget)
        layout.addWidget(scroll)

        # Load movies
        self.load_movies()

    def load_movies(self):
        """Load movies from the 2.0 Movies folder."""
        try:
            # Clear existing content
            self.clear_content()

            # Find the MediaSorter root folder
            root_folder = self.find_mediaSorter_root()

            # Look for 2.0 Movies folder
            movies_folder = root_folder / "2.0 Movies"

            # Log the path we're using
            print(f"Looking for movies in: {movies_folder}")

            if not movies_folder.exists():
                # Try to create the folder
                try:
                    movies_folder.mkdir(parents=True, exist_ok=True)
                    QMessageBox.information(self, "Movies Folder Created",
                                          f"Created the '2.0 Movies' folder at {movies_folder}")
                except Exception as e:
                    QMessageBox.warning(self, "Movies Folder Not Found",
                                       f"The folder '2.0 Movies' was not found and could not be created at {movies_folder}.\n"
                                       f"Error: {str(e)}")
                    return

            # Scan for movies
            self.all_movies = []
            self.movies_by_category = {
                "Recently Added": [],
                "All Movies": [],
                "Action": [],
                "Comedy": [],
                "Drama": [],
                "Sci-Fi": []
            }

            # Track processed folders to avoid duplicates
            processed_folders = set()

            # Walk through the movies folder
            for root, dirs, files in os.walk(movies_folder):
                for file in files:
                    if file.endswith(('.mp4', '.mkv', '.avi', '.mov')):
                        file_path = os.path.join(root, file)
                        movie_folder = Path(os.path.dirname(file_path))

                        # Skip if we've already processed this folder
                        if movie_folder in processed_folders:
                            continue

                        # Add to processed folders
                        processed_folders.add(movie_folder)

                        # Extract movie data from folder name
                        folder_name = movie_folder.name
                        movie_data = {
                            'title': folder_name,
                            'file_path': file_path
                        }

                        # Try to extract year from folder name (e.g., "Movie Name (2023)")
                        year_match = re.search(r'\((\d{4})\)$', folder_name)
                        if year_match:
                            movie_data['year'] = year_match.group(1)
                            # Remove year from title
                            movie_data['title'] = folder_name.replace(f" ({year_match.group(1)})", "")

                        # Check for metadata in different possible locations

                        # First check for folder_name_info folder (from search tab)
                        info_folder_name = f"{folder_name}_info"
                        info_folder = movie_folder / info_folder_name

                        # Check if the info folder exists
                        if info_folder.exists():
                            # Try to load metadata from media_info.json (the correct file name)
                            metadata_file = info_folder / "media_info.json"
                            if metadata_file.exists():
                                try:
                                    with open(metadata_file, 'r', encoding='utf-8') as f:
                                        metadata = json.load(f)
                                        # Extract relevant fields from the TMDB-style metadata
                                        movie_data.update({
                                            'title': metadata.get('title', movie_data['title']),
                                            'year': metadata.get('release_date', '')[:4] if metadata.get('release_date') else '',
                                            'overview': metadata.get('overview', ''),
                                            'genres': [g['name'] for g in metadata.get('genres', [])],
                                            'rating': metadata.get('vote_average', ''),
                                            'runtime': metadata.get('runtime', '')
                                        })
                                        print(f"Loaded metadata from {info_folder_name}/media_info.json")
                                except Exception as e:
                                    print(f"Error loading metadata from {info_folder_name}/media_info.json: {str(e)}")

                            # Add poster path if available (from the posters folder)
                            poster_path = info_folder / "posters" / "main_poster.jpg"
                            if poster_path.exists():
                                movie_data['poster_path'] = str(poster_path)
                                print(f"Found poster at {info_folder_name}/posters/main_poster.jpg")

                            # Also check for backdrop
                            backdrop_path = info_folder / "backdrops" / "main_backdrop.jpg"
                            if backdrop_path.exists():
                                movie_data['backdrop_path'] = str(backdrop_path)
                                print(f"Found backdrop at {info_folder_name}/backdrops/main_backdrop.jpg")

                        # Also check for metadata folder (backward compatibility)
                        metadata_folder = movie_folder / "metadata"
                        if metadata_folder.exists():
                            # Try to load metadata if it exists
                            metadata_file = metadata_folder / "movie_info.json"
                            if metadata_file.exists():
                                try:
                                    with open(metadata_file, 'r', encoding='utf-8') as f:
                                        metadata = json.load(f)
                                        movie_data.update(metadata)
                                except:
                                    pass

                            # Add poster path if available
                            poster_path = metadata_folder / "poster.jpg"
                            if poster_path.exists():
                                movie_data['poster_path'] = str(poster_path)

                        # Add to all movies
                        self.all_movies.append(movie_data)

                        # Add to categories
                        self.movies_by_category["All Movies"].append(movie_data)

                        # Add to recently added (sort later)
                        self.movies_by_category["Recently Added"].append(movie_data)

                        # Add to genres if available
                        genres = movie_data.get('genres', [])
                        for genre in genres:
                            if genre in self.movies_by_category:
                                self.movies_by_category[genre].append(movie_data)
                            else:
                                self.movies_by_category[genre] = [movie_data]

            # Sort recently added by modification time
            self.movies_by_category["Recently Added"].sort(
                key=lambda m: os.path.getmtime(m['file_path']),
                reverse=True
            )

            # Limit to 10 movies
            self.movies_by_category["Recently Added"] = self.movies_by_category["Recently Added"][:10]

            # Display categories
            self.display_categories()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load movies: {str(e)}")

    def display_categories(self):
        """Display movie categories."""
        # Add Recently Added section
        if self.movies_by_category["Recently Added"]:
            self.add_category("Recently Added", self.movies_by_category["Recently Added"])

        # Add genres with movies
        for category, movies in self.movies_by_category.items():
            if category not in ["Recently Added", "All Movies"] and movies:
                self.add_category(category, movies)

    def add_category(self, category_name, movies):
        """Add a category of movies to the layout."""
        # Add header
        header = CategoryHeader(category_name)
        self.content_layout.addWidget(header)

        # Create horizontal layout for movies
        movie_layout = QHBoxLayout()
        movie_layout.setSpacing(10)

        # Add movie posters
        for movie in movies[:6]:  # Show up to 6 movies per row
            poster = MoviePoster(movie)
            poster.clicked.connect(self.show_movie_details)
            movie_layout.addWidget(poster)

        # Add spacer to push movies to the left
        movie_layout.addStretch()

        self.content_layout.addLayout(movie_layout)

    def clear_content(self):
        """Clear the content layout."""
        while self.content_layout.count():
            item = self.content_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
            elif item.layout():
                self.clear_layout(item.layout())

    def clear_layout(self, layout):
        """Recursively clear a layout."""
        while layout.count():
            item = layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
            elif item.layout():
                self.clear_layout(item.layout())

    def show_movie_details(self, movie_data):
        """Show the movie details dialog."""
        dialog = MovieDetailsDialog(movie_data, self)
        dialog.play_requested.connect(self.play_movie)

        # Add a button to add/edit metadata
        dialog.add_metadata_button = QPushButton("Add/Edit Metadata")
        dialog.add_metadata_button.clicked.connect(lambda: self.add_movie_metadata(movie_data))
        dialog.button_layout.addWidget(dialog.add_metadata_button)

        dialog.exec_()

    def play_movie(self, file_path):
        """Play a movie using the configured player."""
        # Use the PlayerLauncher to play the movie with the configured player
        self.movie_player = PlayerLauncher.play_movie(file_path, self)

    def search_movies(self, query):
        """Search movies by title or metadata."""
        if not query:
            # If search is empty, show all categories
            self.clear_content()
            self.display_categories()
            return

        # Filter movies by search query
        query = query.lower()
        results = []

        for movie in self.all_movies:
            title = movie.get('title', '').lower()
            overview = movie.get('overview', '').lower()

            if query in title or query in overview:
                results.append(movie)

        # Display search results
        self.clear_content()

        if results:
            # Add search results header
            header = CategoryHeader(f"Search Results for '{query}'")
            self.content_layout.addWidget(header)

            # Create grid layout for results
            grid = QGridLayout()
            grid.setSpacing(10)

            # Add movie posters to grid
            row, col = 0, 0
            max_cols = 5

            for movie in results:
                poster = MoviePoster(movie)
                poster.clicked.connect(self.show_movie_details)
                grid.addWidget(poster, row, col)

                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1

            self.content_layout.addLayout(grid)
        else:
            # No results found
            no_results = QLabel("No movies found matching your search.")
            no_results.setAlignment(Qt.AlignCenter)
            no_results.setStyleSheet("font-size: 16px; color: #888888; padding: 40px;")
            self.content_layout.addWidget(no_results)

    def apply_filter(self, filter_text):
        """Apply a filter to the movie library."""
        # Reset search
        self.search_input.clear()

        # Apply filter
        if filter_text == "All":
            self.clear_content()
            self.display_categories()
        elif filter_text == "Recently Added":
            self.clear_content()
            if self.movies_by_category["Recently Added"]:
                self.add_category("Recently Added", self.movies_by_category["Recently Added"])
        else:
            # Filter by category
            self.clear_content()
            for category, movies in self.movies_by_category.items():
                if category == filter_text and movies:
                    self.add_category(category, movies)

    def add_folder(self):
        """Add a folder of movies to the library."""
        folder = QFileDialog.getExistingDirectory(
            self, "Select Movie Folder", "",
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )

        if not folder:
            return

        try:
            # Find the MediaSorter root folder
            root_folder = self.find_mediaSorter_root()

            # Look for 2.0 Movies folder
            dest_folder = root_folder / "2.0 Movies"

            # Log the path we're using
            print(f"Adding movies to: {dest_folder}")

            if not dest_folder.exists():
                # Create the folder if it doesn't exist
                try:
                    dest_folder.mkdir(parents=True, exist_ok=True)
                    QMessageBox.information(self, "Folder Created",
                                          f"Created the '2.0 Movies' folder at {dest_folder}")
                except Exception as e:
                    QMessageBox.warning(self, "Error", f"Could not create the movies folder: {str(e)}")
                    return

            # Scan the selected folder for movie files
            movie_files = []
            for root, dirs, files in os.walk(folder):
                for file in files:
                    if file.endswith(('.mp4', '.mkv', '.avi', '.mov')):
                        movie_files.append(os.path.join(root, file))

            if not movie_files:
                QMessageBox.information(self, "No Movies", "No movie files found in the selected folder.")
                return

            # Ask for confirmation
            confirm = QMessageBox.question(
                self, "Import Movies",
                f"Found {len(movie_files)} movie files. Import them to your library?",
                QMessageBox.Yes | QMessageBox.No
            )

            if confirm == QMessageBox.Yes:
                # Import movies
                from movie_code.movie_handler import process_gui_movies
                total, sorted_count, unsorted_count = process_gui_movies(movie_files)

                QMessageBox.information(
                    self, "Import Complete",
                    f"Successfully imported {sorted_count} movies.\n"
                    f"Failed to import {unsorted_count} movies."
                )

                # Reload library
                self.load_movies()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to import movies: {str(e)}")

    def scan_library(self):
        """Scan the library for new movies."""
        try:
            # Reload movies
            self.load_movies()
            QMessageBox.information(self, "Scan Complete", "Library scan completed successfully.")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to scan library: {str(e)}")

    def refresh_metadata(self):
        """Refresh metadata for all movies in the library."""
        try:
            # Find the MediaSorter root folder
            root_folder = self.find_mediaSorter_root()
            movies_folder = root_folder / "2.0 Movies"

            # Log the path we're using
            print(f"Refreshing metadata for movies in: {movies_folder}")

            if not movies_folder.exists():
                QMessageBox.warning(self, "Movies Folder Not Found",
                                   f"The folder '2.0 Movies' was not found at {movies_folder}.")
                return

            # Show progress dialog
            from PyQt5.QtWidgets import QProgressDialog
            progress = QProgressDialog("Refreshing metadata...", "Cancel", 0, 100, self)
            progress.setWindowTitle("Refresh Metadata")
            progress.setWindowModality(Qt.WindowModal)
            progress.setValue(0)
            progress.show()

            # Scan for movie folders
            movie_folders = []
            for item in movies_folder.iterdir():
                if item.is_dir():
                    movie_folders.append(item)

            if not movie_folders:
                progress.close()
                QMessageBox.information(self, "No Movies Found",
                                      "No movie folders found in the 2.0 Movies directory.")
                return

            # Update progress dialog
            progress.setMaximum(len(movie_folders))

            # Process each movie folder
            processed_count = 0
            metadata_found = 0

            for i, movie_folder in enumerate(movie_folders):
                # Update progress
                progress.setValue(i)
                progress.setLabelText(f"Processing: {movie_folder.name}")

                if progress.wasCanceled():
                    break

                # Check for metadata in different possible locations
                found_metadata = False

                # First check for folder_name_info folder (from search tab)
                folder_name = movie_folder.name
                info_folder_name = f"{folder_name}_info"
                info_folder = movie_folder / info_folder_name

                # Check if the info folder exists
                if info_folder.exists():
                    # Try to load metadata if it exists
                    metadata_file = info_folder / "media_info.json"
                    if metadata_file.exists():
                        print(f"Found metadata at {info_folder_name}/media_info.json")
                        found_metadata = True

                    # Check for poster
                    poster_path = info_folder / "posters" / "main_poster.jpg"
                    if poster_path.exists():
                        print(f"Found poster at {info_folder_name}/posters/main_poster.jpg")

                # Also check for metadata folder (backward compatibility)
                if not found_metadata:
                    metadata_folder = movie_folder / "metadata"
                    if metadata_folder.exists():
                        # Check if metadata file exists
                        metadata_file = metadata_folder / "movie_info.json"
                        if metadata_file.exists():
                            print(f"Found metadata at metadata/movie_info.json")
                            found_metadata = True

                # If we found metadata in either location
                if found_metadata:
                    metadata_found += 1
                    processed_count += 1

            # Complete progress
            progress.setValue(len(movie_folders))
            progress.close()

            # Show results
            QMessageBox.information(
                self,
                "Refresh Complete",
                f"Processed {processed_count} movie folders.\n"
                f"Found {metadata_found} movies with metadata."
            )

            # Reload the library to show updated metadata
            self.load_movies()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to refresh metadata: {str(e)}")

    def add_movie_metadata(self, movie_data):
        """Add or edit metadata for a movie."""
        try:
            # Get the movie folder path
            file_path = movie_data.get('file_path')
            if not file_path:
                QMessageBox.warning(self, "Error", "No file path found for this movie.")
                return

            movie_folder = Path(os.path.dirname(file_path))

            # Log the movie folder we're working with
            print(f"Adding metadata to movie in: {movie_folder}")

            # Create metadata folder if it doesn't exist
            metadata_folder = movie_folder / "metadata"
            metadata_folder.mkdir(parents=True, exist_ok=True)

            # Create a dialog to edit metadata
            dialog = QDialog(self)
            dialog.setWindowTitle("Edit Movie Metadata")
            dialog.setMinimumWidth(500)
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #1a1a1a;
                    color: white;
                }
                QLabel {
                    color: white;
                }
                QLineEdit, QTextEdit {
                    background-color: #2d2d2d;
                    color: white;
                    border: 1px solid #3d3d3d;
                    border-radius: 4px;
                    padding: 8px;
                }
                QPushButton {
                    background-color: #0078d7;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #1e90ff;
                }
            """)

            layout = QVBoxLayout(dialog)

            # Title field
            title_layout = QHBoxLayout()
            title_label = QLabel("Title:")
            title_label.setMinimumWidth(100)
            title_input = QLineEdit(movie_data.get('title', ''))
            title_layout.addWidget(title_label)
            title_layout.addWidget(title_input)
            layout.addLayout(title_layout)

            # Year field
            year_layout = QHBoxLayout()
            year_label = QLabel("Year:")
            year_label.setMinimumWidth(100)
            year_input = QLineEdit(str(movie_data.get('year', '')))
            year_layout.addWidget(year_label)
            year_layout.addWidget(year_input)
            layout.addLayout(year_layout)

            # Overview field
            overview_layout = QVBoxLayout()
            overview_label = QLabel("Overview:")
            overview_input = QTextEdit(movie_data.get('overview', ''))
            overview_input.setMinimumHeight(100)
            overview_layout.addWidget(overview_label)
            overview_layout.addWidget(overview_input)
            layout.addLayout(overview_layout)

            # Genres field
            genres_layout = QHBoxLayout()
            genres_label = QLabel("Genres:")
            genres_label.setMinimumWidth(100)
            genres_input = QLineEdit(', '.join(movie_data.get('genres', [])))
            genres_layout.addWidget(genres_label)
            genres_layout.addWidget(genres_input)
            layout.addLayout(genres_layout)

            # Poster image
            poster_layout = QHBoxLayout()
            poster_label = QLabel("Poster:")
            poster_label.setMinimumWidth(100)
            poster_path_input = QLineEdit(movie_data.get('poster_path', ''))
            poster_path_input.setReadOnly(True)
            poster_browse_button = QPushButton("Browse")
            poster_layout.addWidget(poster_label)
            poster_layout.addWidget(poster_path_input)
            poster_layout.addWidget(poster_browse_button)
            layout.addLayout(poster_layout)

            # Buttons
            button_layout = QHBoxLayout()
            save_button = QPushButton("Save")
            cancel_button = QPushButton("Cancel")
            button_layout.addWidget(save_button)
            button_layout.addWidget(cancel_button)
            layout.addLayout(button_layout)

            # Connect signals
            cancel_button.clicked.connect(dialog.reject)
            save_button.clicked.connect(dialog.accept)

            def browse_poster():
                file_path, _ = QFileDialog.getOpenFileName(
                    dialog, "Select Poster Image", "", "Image Files (*.jpg *.jpeg *.png)"
                )
                if file_path:
                    poster_path_input.setText(file_path)

            poster_browse_button.clicked.connect(browse_poster)

            # Show dialog
            if dialog.exec_() == QDialog.Accepted:
                # Save metadata
                metadata = {
                    'title': title_input.text(),
                    'year': year_input.text(),
                    'overview': overview_input.toPlainText(),
                    'genres': [g.strip() for g in genres_input.text().split(',') if g.strip()],
                    'file_path': str(file_path),
                    'original_filename': os.path.basename(file_path)
                }

                # Save metadata to JSON file
                metadata_file = metadata_folder / "movie_info.json"
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=4)

                # Copy poster if selected
                selected_poster = poster_path_input.text()
                if selected_poster and os.path.exists(selected_poster):
                    poster_dest = metadata_folder / "poster.jpg"
                    shutil.copy2(selected_poster, poster_dest)
                    metadata['poster_path'] = str(poster_dest)

                QMessageBox.information(dialog, "Success", "Metadata saved successfully!")

                # Reload the library to show updated metadata
                self.load_movies()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save metadata: {str(e)}")
