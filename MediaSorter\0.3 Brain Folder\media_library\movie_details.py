from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QWidget, QScrollArea, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QFont, QColor, QPalette
from .movie_player import MoviePlayer
from .player_settings import PlayerSettingsDialog

class MovieDetailsDialog(QDialog):
    """A dialog showing detailed movie information with playback options."""

    play_requested = pyqtSignal(str)  # Emits the movie file path when play is clicked

    def __init__(self, movie_data, parent=None):
        super().__init__(parent)
        self.movie_data = movie_data
        self.setup_ui()

    def setup_ui(self):
        """Initialize the dialog UI."""
        self.setWindowTitle("Movie Details")
        self.setMinimumSize(800, 600)
        self.setStyleSheet("""
            QDialog {
                background: #141414;
                color: white;
            }
            QLabel {
                color: white;
            }
            QPushButton {
                background: #E50914;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #F40D17;
            }
            QPushButton#secondary {
                background: rgba(255, 255, 255, 0.1);
            }
            QPushButton#secondary:hover {
                background: rgba(255, 255, 255, 0.2);
            }
            QScrollArea {
                border: none;
                background: transparent;
            }
            QWidget#content {
                background: transparent;
            }
        """)

        # Create main layout
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create header with backdrop
        header = QWidget()
        header.setMinimumHeight(400)
        header.setStyleSheet(f"""
            QWidget {{
                background-image: url({self.movie_data.get('backdrop_path', '')});
                background-position: center;
                background-repeat: no-repeat;
                background-size: cover;
            }}
        """)

        # Add gradient overlay
        header_layout = QVBoxLayout(header)
        header_layout.setContentsMargins(40, 40, 40, 40)
        header_layout.setAlignment(Qt.AlignBottom)

        # Add title and year
        title = QLabel(self.movie_data.get('title', 'Unknown Title'))
        title.setFont(QFont('Arial', 24, QFont.Bold))
        title.setStyleSheet("color: white; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);")
        header_layout.addWidget(title)

        if 'year' in self.movie_data:
            year = QLabel(str(self.movie_data['year']))
            year.setFont(QFont('Arial', 16))
            year.setStyleSheet("color: rgba(255,255,255,0.7);")
            header_layout.addWidget(year)

        # Add play buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        play_button = QPushButton("▶ Play")
        play_button.clicked.connect(self.play_movie)
        buttons_layout.addWidget(play_button)

        more_info_button = QPushButton("ℹ More Info")
        more_info_button.setObjectName("secondary")
        buttons_layout.addWidget(more_info_button)

        # Add player settings button
        settings_button = QPushButton("⚙ Player Settings")
        settings_button.setObjectName("secondary")
        settings_button.clicked.connect(self.show_player_settings)
        buttons_layout.addWidget(settings_button)

        buttons_layout.addStretch()
        header_layout.addLayout(buttons_layout)

        # Store the buttons layout for external access
        self.button_layout = buttons_layout

        layout.addWidget(header)

        # Create scrollable content area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        content = QWidget()
        content.setObjectName("content")
        content_layout = QVBoxLayout(content)
        content_layout.setContentsMargins(40, 20, 40, 20)

        # Add overview
        if 'overview' in self.movie_data:
            overview_label = QLabel("Overview")
            overview_label.setFont(QFont('Arial', 18, QFont.Bold))
            content_layout.addWidget(overview_label)

            overview = QLabel(self.movie_data['overview'])
            overview.setWordWrap(True)
            overview.setStyleSheet("color: rgba(255,255,255,0.7);")
            content_layout.addWidget(overview)
            content_layout.addSpacing(20)

        # Add metadata
        metadata_layout = QHBoxLayout()

        if 'runtime' in self.movie_data:
            runtime_label = QLabel(f"Runtime: {self.movie_data['runtime']} min")
            runtime_label.setStyleSheet("color: rgba(255,255,255,0.7);")
            metadata_layout.addWidget(runtime_label)

        if 'rating' in self.movie_data:
            rating_label = QLabel(f"Rating: {self.movie_data['rating']}")
            rating_label.setStyleSheet("color: rgba(255,255,255,0.7);")
            metadata_layout.addWidget(rating_label)

        if 'genres' in self.movie_data:
            genres_label = QLabel(f"Genres: {', '.join(self.movie_data['genres'])}")
            genres_label.setStyleSheet("color: rgba(255,255,255,0.7);")
            metadata_layout.addWidget(genres_label)

        metadata_layout.addStretch()
        content_layout.addLayout(metadata_layout)

        scroll.setWidget(content)
        layout.addWidget(scroll)

    def play_movie(self):
        """Emit signal to play the movie."""
        if 'file_path' in self.movie_data:
            self.play_requested.emit(self.movie_data['file_path'])
            self.accept()  # Close the dialog

    def show_player_settings(self):
        """Show the player settings dialog."""
        dialog = PlayerSettingsDialog(self)
        dialog.exec_()

    def keyPressEvent(self, event):
        """Handle keyboard shortcuts."""
        if event.key() == Qt.Key_Escape:
            self.reject()
        elif event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.play_movie()
        else:
            super().keyPressEvent(event)