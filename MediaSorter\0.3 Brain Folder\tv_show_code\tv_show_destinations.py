from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QLineEdit, QCheckBox, QFileDialog,
                             QFrame)
from PyQt5.QtCore import Qt
from settings.settings_manager import save_settings
import uuid

def create_new_tv_destination(number=None):
    return {
        "id": str(uuid.uuid4()),
        "number": number if number is not None else 0,
        "name": "New TV Show Destination",
        "path": "",
        "enabled": False,
        "sort_type": "TV Shows"
    }

def get_tv_destinations():
    from settings.settings_manager import load_settings, save_settings
    settings = load_settings()
    updated = False
    for folder in settings.get("custom_folders", []):
        if folder.get("sort_type") == "TV Shows" and "id" not in folder:
            folder["id"] = str(uuid.uuid4())
            updated = True
    if updated:
        save_settings(settings)
    return [folder for folder in settings.get("custom_folders", [])
            if folder.get("sort_type") == "TV Shows"]

class TVShowDestinationWidget(QWidget):
    def __init__(self, folder_data, settings):
        super().__init__()
        self.folder_data = folder_data
        self.settings = settings
        self.number = folder_data.get('number', 0)
        self.setFixedHeight(90)
        self.setup_ui()
        self.apply_styles()

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(6)
        self.setLayout(layout)

        # Main frame
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame_layout = QVBoxLayout()
        frame_layout.setContentsMargins(10, 10, 10, 10)
        frame_layout.setSpacing(8)
        frame.setLayout(frame_layout)
        frame.setMinimumWidth(380)

        # Row 1: Enable/disable checkbox and name
        row1 = QHBoxLayout()
        self.enabled_checkbox = QCheckBox("Enabled")
        self.enabled_checkbox.setChecked(self.folder_data.get("enabled", False))
        self.enabled_checkbox.stateChanged.connect(self.save_changes)
        row1.addWidget(self.enabled_checkbox)
        name_label = QLabel("Name:")
        row1.addWidget(name_label)
        self.name_input = QLineEdit(self.folder_data.get("name", ""))
        self.name_input.setPlaceholderText("Destination Name")
        self.name_input.setMinimumWidth(120)
        self.name_input.setMaximumWidth(200)
        self.name_input.setFixedHeight(24)
        self.name_input.textChanged.connect(self.save_changes)
        row1.addWidget(self.name_input)
        row1.addStretch()
        frame_layout.addLayout(row1)

        # Row 2: Path, browse, delete, number
        row2 = QHBoxLayout()
        path_label = QLabel("Path:")
        row2.addWidget(path_label)
        self.path_input = QLineEdit(self.folder_data.get("path", ""))
        self.path_input.setPlaceholderText("Destination Path")
        self.path_input.setMinimumWidth(180)
        self.path_input.setMaximumWidth(300)
        self.path_input.setFixedHeight(24)
        self.path_input.textChanged.connect(self.save_changes)
        row2.addWidget(self.path_input)
        browse_button = QPushButton("Browse")
        browse_button.setFixedHeight(24)
        browse_button.clicked.connect(self.browse_folder)
        row2.addWidget(browse_button)
        delete_button = QPushButton("Delete")
        delete_button.setFixedHeight(24)
        delete_button.clicked.connect(self.delete_destination)
        row2.addWidget(delete_button)
        # Show the number next to the delete button
        number_label = QLabel(f"#{self.number}")
        number_label.setStyleSheet("color: #FFD700; font-weight: bold; font-size: 15px;")
        row2.addWidget(number_label)
        row2.addStretch()
        frame_layout.addLayout(row2)

        layout.addWidget(frame)

    def apply_styles(self):
        self.setStyleSheet("""
            QFrame {
                background-color: #23272e;
                border: 1.5px solid #444;
                border-radius: 7px;
                margin-bottom: 4px;
            }
            QLabel, QCheckBox {
                color: #f1f1f1;
                font-size: 13px;
            }
            QLineEdit {
                padding: 2px 6px;
                border: 1px solid #555;
                border-radius: 4px;
                background-color: #181a20;
                color: #f1f1f1;
                min-height: 20px;
                font-size: 13px;
            }
            QLineEdit:placeholder {
                color: #b0b0b0;
            }
            QPushButton {
                padding: 2px 10px;
                background-color: #007bff;
                color: #fff;
                border: none;
                border-radius: 4px;
                min-height: 20px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QCheckBox {
                min-height: 20px;
            }
        """)

    def browse_folder(self):
        folder = QFileDialog.getExistingDirectory(
            self, "Select Destination Folder",
            self.path_input.text() or "."
        )
        if folder:
            self.path_input.setText(folder)
            self.save_changes()

    def save_changes(self):
        self.folder_data.update({
            "name": self.name_input.text(),
            "path": self.path_input.text(),
            "enabled": self.enabled_checkbox.isChecked(),
            "id": self.folder_data.get("id"),
            "number": self.folder_data.get("number"),
            "sort_type": self.folder_data.get("sort_type", "TV Shows")
        })

        # Update settings
        if "custom_folders" not in self.settings:
            self.settings["custom_folders"] = []
        
        # Find and update the folder in settings by id
        for i, folder in enumerate(self.settings["custom_folders"]):
            if folder.get("id") == self.folder_data.get("id"):
                self.settings["custom_folders"][i] = self.folder_data
                break
        else:
            # If not found, append it
            self.settings["custom_folders"].append(self.folder_data)

        save_settings(self.settings)
        # Reload parent tab to keep UI in sync
        parent = self.parent()
        while parent is not None and not hasattr(parent, 'load_destinations'):
            parent = parent.parent()
        if parent is not None and hasattr(parent, 'load_destinations'):
            parent.load_destinations()

    def delete_destination(self):
        # Remove from settings (match by unique id)
        if "custom_folders" in self.settings:
            self.settings["custom_folders"] = [
                folder for folder in self.settings["custom_folders"]
                if folder.get("id") != self.folder_data.get("id")
            ]
            save_settings(self.settings)
        
        # Remove widget
        self.deleteLater()
        # Notify parent tab to reload destinations
        parent = self.parent()
        while parent is not None and not hasattr(parent, 'load_destinations'):
            parent = parent.parent()
        if parent is not None and hasattr(parent, 'load_destinations'):
            parent.load_destinations() 