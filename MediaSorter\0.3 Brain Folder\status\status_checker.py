# status_checker.py v2.2
# Updated to better handle success messages for both custom and default folders

from pathlib import Path
import re
import logging

def find_brain_folder():
    """Find the Brain folder path"""
    current = Path(__file__).resolve().parent.parent
    return current

def get_status_summary():
    """Get combined status summary for both file and movie sorting"""
    brain_folder = find_brain_folder()
    logs_folder = brain_folder / "logs"
    file_log = logs_folder / "file_sorter_log_4.txt"
    movie_log = logs_folder / "movie_sorter_log_3.txt"

    # Initialize counters
    stats = {
        'files': {'total': 0, 'sorted': 0, 'unsorted': 0, 'last': 'N/A'},
        'movies': {'total': 0, 'sorted': 0, 'unsorted': 0, 'last': 'N/A'}
    }

    # Process file sorter log
    if file_log.exists():
        try:
            lines = file_log.read_text(encoding="utf-8").splitlines()
            for line in lines:
                if "Processing file:" in line:
                    stats['files']['total'] += 1
                    match = re.search(r"Processing file: (.+)$", line)
                    if match:
                        stats['files']['last'] = match.group(1)
                # Count both "Successfully moved" and "Copied" as successes
                if "Successfully moved:" in line or ("Copied" in line and "Unsorted" not in line):
                    stats['files']['sorted'] += 1
                if "Failed" in line or "Skipped" in line:
                    stats['files']['unsorted'] += 1
        except Exception as e:
            logging.error(f"Error reading file sorter log: {e}")

    # Process movie sorter log
    if movie_log.exists():
        try:
            lines = movie_log.read_text(encoding="utf-8").splitlines()
            for line in lines:
                if "Processing movie:" in line:
                    stats['movies']['total'] += 1
                    match = re.search(r"Processing movie: (.+)$", line)
                    if match:
                        stats['movies']['last'] = match.group(1)
                # Count both old and new success messages
                if "Successfully sorted movie:" in line:
                    stats['movies']['sorted'] += 1
                if "Failed to sort movie" in line or "Error processing movie" in line:
                    stats['movies']['unsorted'] += 1
        except Exception as e:
            logging.error(f"Error reading movie sorter log: {e}")

    # Format the summary
    summary = "=== File Sorting Status ===\n"
    summary += f"Total Files Processed: {stats['files']['total']}\n"
    summary += f"Successfully Sorted: {stats['files']['sorted']}\n"
    summary += f"Unsorted/Failed: {stats['files']['unsorted']}\n"
    summary += f"Last File Processed: {stats['files']['last']}\n\n"

    summary += "=== Movie Sorting Status ===\n"
    summary += f"Total Movies Processed: {stats['movies']['total']}\n"
    summary += f"Successfully Sorted: {stats['movies']['sorted']}\n"
    summary += f"Unsorted/Failed: {stats['movies']['unsorted']}\n"
    summary += f"Last Movie Processed: {stats['movies']['last']}"

    return summary