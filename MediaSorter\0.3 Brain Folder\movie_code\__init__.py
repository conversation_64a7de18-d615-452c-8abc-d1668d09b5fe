"""
MediaSorter Movie Handling Package.
This package contains functionality for sorting movie files into organized directories.
"""

from .movie_handler import (
    process_gui_movies,
    sort_movie,
    get_movies_folder
)
from .movie_tab import MovieTab
from .movie_destinations import (
    MovieDestinationWidget,
    create_new_movie_destination,
    get_movie_destinations
)

__all__ = [
    'process_gui_movies',
    'sort_movie',
    'get_movies_folder',
    'MovieTab',
    'MovieDestinationWidget',
    'create_new_movie_destination',
    'get_movie_destinations'
]
