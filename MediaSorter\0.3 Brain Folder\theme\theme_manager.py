# theme_manager.py v1.0
from PyQt5.QtGui import QPalette, QColor
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QWidget, QLabel, QTextEdit, QListWidget, QLineEdit
from enum import Enum

class Theme(Enum):
    LIGHT = "light"
    DARK = "dark"

class ThemeManager:
    def __init__(self):
        self.current_theme = Theme.DARK

    def get_palette(self) -> QPalette:
        """Get the current theme's palette."""
        return self.get_theme_palette(self.current_theme)

    def get_stylesheet(self) -> str:
        """Get the current theme's stylesheet."""
        return self.get_theme_stylesheet(self.current_theme)

    def toggle_theme(self):
        """Toggle between light and dark themes."""
        self.current_theme = Theme.LIGHT if self.current_theme == Theme.DARK else Theme.DARK

    def is_dark_mode(self) -> bool:
        """Check if dark mode is currently active."""
        return self.current_theme == Theme.DARK

    def apply_theme(self, widget, theme: Theme = None):
        """Apply the specified theme (or current theme if none specified) to the widget."""
        if theme is not None:
            self.current_theme = theme

        # Apply palette and stylesheet to main widget
        widget.setPalette(self.get_palette())
        widget.setStyleSheet(self.get_stylesheet())

        # Apply to all child widgets recursively
        for child in widget.findChildren(QWidget):
            child.setPalette(self.get_palette())
            child.setStyleSheet(self.get_stylesheet())

            # Ensure text colors are properly set for specific widgets
            if isinstance(child, QLabel):
                child.setStyleSheet(f"color: {'white' if self.is_dark_mode() else 'black'};")
            elif isinstance(child, QTextEdit) or isinstance(child, QLineEdit):
                child.setStyleSheet(f"""
                    background-color: {self.get_bg_color()};
                    color: {self.get_text_color()};
                    border: 1px solid {self.get_border_color()};
                    border-radius: 4px;
                    padding: 4px;
                """)
            elif isinstance(child, QListWidget):
                child.setStyleSheet(f"""
                    QListWidget {{
                        background-color: {self.get_bg_color()};
                        color: {self.get_text_color()};
                        border: 1px solid {self.get_border_color()};
                        border-radius: 4px;
                        padding: 4px;
                    }}
                    QListWidget::item {{
                        color: {self.get_text_color()};
                        background-color: transparent;
                        padding: 4px;
                    }}
                    QListWidget::item:selected {{
                        background-color: {self.get_highlight_color()};
                        color: {self.get_text_color()};
                    }}
                """)

    def get_bg_color(self) -> str:
        """Get the appropriate background color for the current theme."""
        return "#2d2d2d" if self.is_dark_mode() else "#ffffff"

    def get_text_color(self) -> str:
        """Get the appropriate text color for the current theme."""
        return "#ffffff" if self.is_dark_mode() else "#000000"

    def get_border_color(self) -> str:
        """Get the appropriate border color for the current theme."""
        return "#404040" if self.is_dark_mode() else "#cccccc"

    def get_highlight_color(self) -> str:
        """Get the appropriate highlight color for the current theme."""
        return "#404040" if self.is_dark_mode() else "#e9ecef"

    def save_theme(self, theme: Theme):
        """Save the current theme preference."""
        self.current_theme = theme
        try:
            import json
            from pathlib import Path

            # Save to theme settings file
            settings_file = Path(__file__).parent / "theme_settings.json"
            settings = {"theme": theme.value}

            with open(settings_file, 'w') as f:
                json.dump(settings, f)

        except Exception as e:
            print(f"Error saving theme settings: {e}")

    @staticmethod
    def get_theme_palette(theme: Theme) -> QPalette:
        palette = QPalette()

        if theme == Theme.DARK:
            # Dark theme colors
            palette.setColor(QPalette.Window, QColor(45, 45, 45))
            palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
            palette.setColor(QPalette.Base, QColor(35, 35, 35))
            palette.setColor(QPalette.AlternateBase, QColor(55, 55, 55))
            palette.setColor(QPalette.ToolTipBase, QColor(255, 255, 255))
            palette.setColor(QPalette.ToolTipText, QColor(255, 255, 255))
            palette.setColor(QPalette.Text, QColor(255, 255, 255))
            palette.setColor(QPalette.Button, QColor(45, 45, 45))
            palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
            palette.setColor(QPalette.BrightText, QColor(255, 255, 255))
            palette.setColor(QPalette.Link, QColor(42, 130, 218))
            palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
            palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
        else:
            # Light theme colors
            palette.setColor(QPalette.Window, QColor(240, 240, 240))
            palette.setColor(QPalette.WindowText, QColor(0, 0, 0))
            palette.setColor(QPalette.Base, QColor(255, 255, 255))
            palette.setColor(QPalette.AlternateBase, QColor(245, 245, 245))
            palette.setColor(QPalette.ToolTipBase, QColor(255, 255, 255))
            palette.setColor(QPalette.ToolTipText, QColor(0, 0, 0))
            palette.setColor(QPalette.Text, QColor(0, 0, 0))
            palette.setColor(QPalette.Button, QColor(240, 240, 240))
            palette.setColor(QPalette.ButtonText, QColor(0, 0, 0))
            palette.setColor(QPalette.BrightText, QColor(0, 0, 0))
            palette.setColor(QPalette.Link, QColor(0, 122, 204))
            palette.setColor(QPalette.Highlight, QColor(0, 120, 215))
            palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))

        return palette

    @staticmethod
    def get_theme_stylesheet(theme: Theme) -> str:
        # Common styles
        common_styles = """
            QWidget {
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton {
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QListWidget {
                border-radius: 4px;
                padding: 4px;
            }
            QProgressBar {
                border-radius: 2px;
                text-align: center;
                min-height: 12px;
            }
            QProgressBar::chunk {
                border-radius: 2px;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                border-radius: 4px;
            }
            QTabBar::tab {
                padding: 8px 16px;
                margin: 2px;
                border-radius: 4px;
            }
            QLabel {
                padding: 2px;
            }
            QGroupBox {
                margin-top: 12px;
                font-weight: bold;
                border: 1px solid;
                border-radius: 4px;
                padding: 12px;
            }
        """

        if theme == Theme.DARK:
            return common_styles + """
                QMainWindow, QDialog, QWidget {
                    background-color: #2d2d2d;
                    color: white;
                }
                QPushButton {
                    background-color: #0078D4;
                    color: white;
                    border: none;
                }
                QPushButton:hover {
                    background-color: #1084D9;
                }
                QPushButton:pressed {
                    background-color: #006CBD;
                }
                QListWidget {
                    background-color: #353535;
                    border: 1px solid #404040;
                    color: white;
                }
                QProgressBar {
                    background-color: #353535;
                    border: 1px solid #404040;
                    color: white;
                }
                QProgressBar::chunk {
                    background-color: #0078D4;
                }
                QTabBar::tab {
                    background-color: #353535;
                    color: white;
                }
                QTabBar::tab:selected {
                    background-color: #0078D4;
                }
                QTabBar::tab:hover:!selected {
                    background-color: #404040;
                }
                QLineEdit {
                    background-color: #353535;
                    border: 1px solid #404040;
                    color: white;
                    padding: 5px;
                }
                QLineEdit:focus {
                    border-color: #0078D4;
                }
                QLabel {
                    color: white;
                }
                QComboBox {
                    background-color: #353535;
                    border: 1px solid #404040;
                    color: white;
                    padding: 5px;
                }
                QComboBox:drop-down {
                    border: none;
                }
                QComboBox QAbstractItemView {
                    background-color: #353535;
                    color: white;
                    selection-background-color: #0078D4;
                }
                QGroupBox {
                    border-color: #404040;
                    color: white;
                }
                QTextEdit {
                    background-color: #353535;
                    color: white;
                    border: 1px solid #404040;
                }
                QScrollBar:vertical {
                    background-color: #2d2d2d;
                    width: 12px;
                }
                QScrollBar::handle:vertical {
                    background-color: #404040;
                    border-radius: 6px;
                    min-height: 20px;
                }
                QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                    border: none;
                    background: none;
                }
            """
        else:
            return common_styles + """
                QMainWindow, QDialog, QWidget {
                    background-color: white;
                    color: black;
                }
                QPushButton {
                    background-color: #0078D4;
                    color: white;
                    border: none;
                }
                QPushButton:hover {
                    background-color: #1084D9;
                }
                QPushButton:pressed {
                    background-color: #006CBD;
                }
                QListWidget {
                    background-color: white;
                    border: 1px solid #cccccc;
                    color: black;
                }
                QProgressBar {
                    background-color: #f0f0f0;
                    border: 1px solid #cccccc;
                    color: black;
                }
                QProgressBar::chunk {
                    background-color: #0078D4;
                }
                QTabBar::tab {
                    background-color: #f0f0f0;
                    color: black;
                }
                QTabBar::tab:selected {
                    background-color: #0078D4;
                    color: white;
                }
                QTabBar::tab:hover:!selected {
                    background-color: #e5e5e5;
                }
                QLineEdit {
                    background-color: white;
                    border: 1px solid #cccccc;
                    color: black;
                    padding: 5px;
                }
                QLineEdit:focus {
                    border-color: #0078D4;
                }
                QLabel {
                    color: black;
                }
                QComboBox {
                    background-color: white;
                    border: 1px solid #cccccc;
                    color: black;
                    padding: 5px;
                }
                QComboBox:drop-down {
                    border: none;
                }
                QComboBox QAbstractItemView {
                    background-color: white;
                    color: black;
                    selection-background-color: #0078D4;
                }
                QGroupBox {
                    border-color: #cccccc;
                    color: black;
                }
                QTextEdit {
                    background-color: white;
                    color: black;
                    border: 1px solid #cccccc;
                }
                QScrollBar:vertical {
                    background-color: #f0f0f0;
                    width: 12px;
                }
                QScrollBar::handle:vertical {
                    background-color: #cccccc;
                    border-radius: 6px;
                    min-height: 20px;
                }
                QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                    border: none;
                    background: none;
                }
            """