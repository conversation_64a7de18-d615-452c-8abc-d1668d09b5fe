"""
Test script to verify all logging configurations are working correctly.
This script will attempt to write to all log files and verify they are created in the correct location.
"""

import os
import sys
import logging
from pathlib import Path
from datetime import datetime

def find_mediasorter_root():
    current = Path(__file__).resolve()
    while current.name != 'MediaSorter':
        current = current.parent
    return current

def test_logging():
    # Setup paths
    main_folder = find_mediasorter_root()
    log_dir = main_folder / '0.3 Brain Folder' / 'logs'
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # List of log files to test
    log_files = [
        'gui_log_1.txt',
        'tv_show_log_3.txt',
        'movie_sorter_log_3.txt',
        'movie_handler_log.txt',
        'file_sorter_log_4.txt'
    ]
    
    print(f"Testing logging to directory: {log_dir}")
    print("=" * 50)
    
    # Test each log file
    for log_file in log_files:
        log_path = log_dir / log_file
        logger = logging.getLogger(log_file)
        logger.setLevel(logging.INFO)
        
        # Remove any existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # Add file handler
        handler = logging.FileHandler(str(log_path))
        handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(handler)
        
        # Write test message
        test_message = f"Test log entry from test_logging.py at {datetime.now()}"
        logger.info(test_message)
        
        # Verify log file exists and contains message
        if log_path.exists():
            with open(log_path, 'r') as f:
                content = f.read()
                if test_message in content:
                    print(f"✅ Successfully wrote to {log_file}")
                else:
                    print(f"❌ Failed to write message to {log_file}")
        else:
            print(f"❌ Failed to create {log_file}")
    
    print("\nChecking for stray logs in root directory...")
    root_logs = list(main_folder.glob('logs/*.txt'))
    if root_logs:
        print("⚠️ Found logs in root directory that should be moved:")
        for log in root_logs:
            print(f"  - {log}")
    else:
        print("✅ No stray logs found in root directory")

if __name__ == "__main__":
    test_logging() 