# settings_toggle_switch.py v1.5
# Updated to handle custom folders and fix path resolution

from pathlib import Path
import json
import os
import logging

# Get path to settings file in Brain folder
brain_folder = Path(__file__).resolve().parent.parent
settings_folder = brain_folder / "settings"
settings_file = settings_folder / "user_settings.json"

def load_settings():
    """Load settings from file with proper error handling"""
    if settings_file.exists():
        try:
            with open(settings_file, "r", encoding="utf-8") as f:
                settings = json.load(f)
                # Ensure custom folders exist with default values
                if "custom_folders" not in settings:
                    settings["custom_folders"] = [
                        {"name": f"Custom Folder {i+1}", "path": "", "enabled": False}
                        for i in range(6)
                    ]
                return settings
        except Exception as e:
            logging.error(f"Error loading settings: {e}")
            return get_default_settings()
    return get_default_settings()

def get_default_settings():
    """Get default settings structure"""
    return {
        "season_sort_path": "", 
        "movie_sort_path": "",
        "use_season_folder": False,
        "use_movie_folder": False,
        "custom_folders": [
            {"name": f"Custom Folder {i+1}", "path": "", "enabled": False}
            for i in range(6)
        ]
    }

def save_settings(settings):
    """Save settings to file with proper error handling"""
    try:
        settings_file.parent.mkdir(parents=True, exist_ok=True)
        with open(settings_file, "w", encoding="utf-8") as f:
            json.dump(settings, f, indent=4)
        return True
    except Exception as e:
        logging.error(f"Error saving settings: {e}")
        return False

def use_season_folder():
    """Check if custom season folder should be used"""
    settings = load_settings()
    use_folder = settings.get("use_season_folder", False)
    if use_folder and settings.get("season_sort_path"):
        path = settings["season_sort_path"]
        if os.path.exists(path):
            return True
        logging.warning(f"Custom season path set but doesn't exist: {path}")
    return False

def get_season_folder_path():
    """Get the path to use for season sorting"""
    settings = load_settings()
    if settings.get("season_sort_path"):
        path = settings["season_sort_path"]
        if os.path.exists(path):
            return path
        logging.warning(f"Custom season path doesn't exist: {path}")
    return str(Path(brain_folder).parent / "0.1 Sorting Folder")

def use_movie_folder():
    """Check if custom movie folder should be used"""
    settings = load_settings()
    use_folder = settings.get("use_movie_folder", False)
    if use_folder and settings.get("movie_sort_path"):
        path = settings["movie_sort_path"]
        if os.path.exists(path):
            return True
        logging.warning(f"Custom movie path set but doesn't exist: {path}")
    return False

def get_movie_folder_path():
    """Get the path to use for movie sorting"""
    settings = load_settings()
    if settings.get("movie_sort_path"):
        path = settings["movie_sort_path"]
        if os.path.exists(path):
            return path
        logging.warning(f"Custom movie path doesn't exist: {path}")
    return str(Path(brain_folder).parent / "0.1 Sorting Folder")

def get_enabled_custom_folders():
    """Get a list of enabled custom folders with their paths and names"""
    settings = load_settings()
    custom_folders = settings.get("custom_folders", [])
    enabled_folders = []
    for folder in custom_folders:
        if folder["enabled"] and folder["path"]:
            if os.path.exists(folder["path"]):
                enabled_folders.append(folder)
            else:
                logging.warning(f"Custom folder path doesn't exist: {folder['path']}")
    return enabled_folders

def get_active_sort_folder():
    """Get the currently active sort folder path based on settings"""
    settings = load_settings()
    
    # Check custom folders first
    custom_folders = settings.get("custom_folders", [])
    for folder in custom_folders:
        if folder["enabled"] and folder["path"]:
            path = Path(folder["path"])
            try:
                # Ensure path exists and is accessible
                if path.exists() and os.access(str(path), os.W_OK):
                    logging.info(f"Using custom folder: {folder['name']} at path: {path}")
                    return str(path)
                else:
                    logging.warning(f"Custom folder {folder['name']} at {path} is not accessible or doesn't exist")
            except Exception as e:
                logging.error(f"Error accessing custom folder {folder['name']}: {e}")
    
    # Then check season folder
    if settings.get("use_season_folder", False):
        path = settings.get("season_sort_path", "")
        if path and Path(path).exists():
            logging.info(f"Using season folder at: {path}")
            return path
        logging.warning(f"Season folder path doesn't exist: {path}")
    
    # Default to sorting folder
    default_path = str(Path(brain_folder).parent / "0.1 Sorting Folder")
    logging.info(f"Using default sorting folder: {default_path}")
    return default_path