import os
from pathlib import Path
import json
from datetime import datetime
import tmdbv3api
from typing import List, Dict, Optional

class MovieManager:
    def __init__(self, movies_folder: str, cache_file: str = "movie_cache.json"):
        self.movies_folder = Path(movies_folder)
        self.cache_file = Path(cache_file)
        self.tmdb = tmdbv3api.TMDb()
        self.movie = tmdbv3api.Movie()
        self.cache = self._load_cache()
        
    def _load_cache(self) -> Dict:
        """Load the movie cache from disk"""
        if self.cache_file.exists():
            try:
                with open(self.cache_file, 'r') as f:
                    return json.load(f)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def _save_cache(self):
        """Save the movie cache to disk"""
        with open(self.cache_file, 'w') as f:
            json.dump(self.cache, f, indent=2)
    
    def set_api_key(self, api_key: str):
        """Set the TMDB API key"""
        self.tmdb.api_key = api_key
    
    def scan_movies(self) -> List[Dict]:
        """Scan the movies folder and update cache with new movies"""
        movie_files = []
        for ext in ['.mp4', '.mkv', '.avi']:
            movie_files.extend(self.movies_folder.glob(f'**/*{ext}'))
        
        movies = []
        for file in movie_files:
            relative_path = file.relative_to(self.movies_folder)
            file_hash = f"{relative_path}:{file.stat().st_mtime}"
            
            # Check if movie is in cache and up to date
            if file_hash in self.cache:
                movies.append(self.cache[file_hash])
                continue
            
            # Clean filename for search
            clean_name = self._clean_filename(file.stem)
            
            # Search TMDB
            try:
                search_results = self.movie.search(clean_name)
                if search_results:
                    movie_data = search_results[0]
                    details = self.movie.details(movie_data.id)
                    
                    # Get poster and backdrop paths
                    poster_path = None
                    backdrop_path = None
                    if details.poster_path:
                        poster_path = f"https://image.tmdb.org/t/p/w500{details.poster_path}"
                    if details.backdrop_path:
                        backdrop_path = f"https://image.tmdb.org/t/p/original{details.backdrop_path}"
                    
                    movie_info = {
                        'title': details.title,
                        'year': datetime.strptime(details.release_date, '%Y-%m-%d').year,
                        'overview': details.overview,
                        'runtime': details.runtime,
                        'rating': details.vote_average,
                        'genres': [genre.name for genre in details.genres],
                        'poster_path': poster_path,
                        'backdrop_path': backdrop_path,
                        'filename': str(relative_path),
                        'added_date': datetime.now().isoformat()
                    }
                    
                    # Cache the result
                    self.cache[file_hash] = movie_info
                    movies.append(movie_info)
            except Exception as e:
                print(f"Error processing {file.name}: {str(e)}")
                continue
        
        # Save updated cache
        self._save_cache()
        return movies
    
    def get_movies(self, category: str = "All Movies") -> List[Dict]:
        """Get movies filtered by category"""
        movies = self.scan_movies()
        
        if category == "All Movies":
            return sorted(movies, key=lambda x: x['title'])
        elif category == "Recently Added":
            return sorted(movies, key=lambda x: x['added_date'], reverse=True)
        elif category == "Favorites":
            # TODO: Implement favorites
            return []
        else:
            # Filter by genre
            return [m for m in movies if category in m.get('genres', [])]
    
    def _clean_filename(self, filename: str) -> str:
        """Clean filename for TMDB search"""
        # Remove common patterns
        patterns = [
            r'S\d{1,2}E\d{1,2}',  # S01E01
            r'Season\s*\d+\s*Episode\s*\d+',  # Season 1 Episode 1
            r'\s*-\s*E\d{1,2}',  # - E01
            r'\s*-\s*EP\d{1,2}',  # - EP01
            r'\(\d{4}\)',  # (2024)
            r'\b(720p|1080p|2160p|4K|HDR|BLURAY|WEB-DL|x264|x265|HEVC)\b',  # Quality tags
            r'[._]'  # Replace dots and underscores with spaces
        ]
        
        import re
        name = filename
        for pattern in patterns:
            name = re.sub(pattern, '', name, flags=re.IGNORECASE)
        
        # If name contains a dash, take the part before it
        if ' - ' in name:
            name = name.split(' - ')[0]
        
        # Clean up extra spaces and special characters
        name = re.sub(r'[^\w\s]', '', name)
        name = ' '.join(name.split())
        
        return name 