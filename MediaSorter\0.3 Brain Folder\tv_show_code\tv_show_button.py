# tv_show_button.py v1.2
# Handles sorting files from the GUI list widget when the Sort button is clicked
# Updated to properly use custom sorting folder settings and remove duplicate functionality

import os
import shutil
import logging
import re
import filecmp
import errno
import time
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any, Callable, Union
from datetime import datetime
from PyQt5.QtWidgets import QPushButton, QMessageBox, QWidget
from settings.settings_toggle_switch import get_active_sort_folder

# Import the process_files function
from tv_show_code.tv_show import process_files, move_to_unsorted

import sys

# Add the parent directory to Python path to fix imports
sys.path.append(str(Path(__file__).resolve().parent.parent))

def find_mediasorter_root():
    current = Path(__file__).resolve()
    while current.name != 'MediaSorter':
        current = current.parent
    return current

# Configure logging
main_folder = find_mediasorter_root()
log_dir = main_folder / '0.3 Brain Folder' / 'logs'
log_dir.mkdir(parents=True, exist_ok=True)

# Create a logger specific to this module
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Remove any existing handlers to avoid duplicates
for handler in logger.handlers[:]:
    logger.removeHandler(handler)

# Add file handler
file_handler = logging.FileHandler(str(log_dir / 'tv_show_log_3.txt'))
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)

# Add console handler for debugging
console_handler = logging.StreamHandler()
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
logger.addHandler(console_handler)

# Type aliases for clarity
PathLike = Union[str, Path]
ProgressCallback = Callable[[int, int, str], None]

def log_file_operation(func):
    """
    Decorator to log file operations with detailed information.

    Args:
        func: The function to wrap with logging.

    Returns:
        The wrapped function with logging.

    Example:
        @log_file_operation
        def move_file(source, dest):
            shutil.move(source, dest)
    """
    def wrapper(*args, **kwargs):
        try:
            logger.info(f"Starting {func.__name__} operation")
            result = func(*args, **kwargs)
            logger.info(f"Completed {func.__name__} operation successfully")
            return result
        except Exception as e:
            logger.error(f"Error in {func.__name__}: {str(e)}", exc_info=True)
            raise
    return wrapper

class FileSorterButton(QPushButton):
    """
    A button widget that handles file sorting operations in the GUI.

    Attributes:
        main_window: Reference to the main application window.
    """

    def __init__(self, parent: Optional[QWidget] = None) -> None:
        """
        Initialize the FileSorterButton.

        Args:
            parent: Parent widget, typically the main window.
        """
        super().__init__("Sort Files", parent)
        self.clicked.connect(self.sort_files)
        self.main_window: Optional[Any] = None
        self.setup_logging()
        logger.info("FileSorterButton initialized")

    def setup_logging(self) -> None:
        """
        Configure logging for the file sorter.
        We're using the module-level logger, so just log initialization.
        """
        logger.info("FileSorterButton logging setup completed")

    def set_main_window(self, main_window: Any) -> None:
        """
        Set the reference to the main application window.

        Args:
            main_window: The main window instance containing the file list widget.
        """
        self.main_window = main_window

    @log_file_operation
    def sort_files(self) -> None:
        """
        Handle the sort files button click event.
        Processes all files in the file list and attempts to sort them.

        Shows appropriate message boxes for success, errors, or empty file lists.
        """
        if not self.main_window or not hasattr(self.main_window, 'file_list'):
            error_msg = "File list not initialized"
            logger.error(error_msg)
            QMessageBox.warning(self, "Error", error_msg)
            return

        file_list = self.main_window.file_list
        if not file_list.file_paths:
            logger.info("No files to sort")
            QMessageBox.information(self, "No Files", "No files to sort")
            return

        try:
            total, sorted_count, unsorted_count = self.process_gui_files(file_list.file_paths)
            file_list.clear()
            success_msg = f"TV shows sorted successfully. Total: {total}, Sorted: {sorted_count}, Unsorted: {unsorted_count}"
            logger.info(success_msg)
            QMessageBox.information(self, "Success", success_msg)
        except Exception as e:
            error_msg = f"Error sorting TV shows: {str(e)}"
            logger.error(error_msg, exc_info=True)
            QMessageBox.critical(self, "Error", error_msg)

    @log_file_operation
    def process_gui_files(self, file_paths: List[str]) -> Tuple[int, int, int]:
        """Process a list of files from the GUI and sort them."""
        logger.info(f"Starting to sort {len(file_paths)} files...")

        # Use the process_files function from tv_show.py
        try:
            return process_files(file_paths)
        except Exception as e:
            logger.error(f"Error in process_files: {str(e)}")
            raise

class MovieSorterButton(QPushButton):
    """
    A button widget that handles movie file sorting operations in the GUI.

    This class provides functionality for sorting movie files into appropriate directories
    based on their names and metadata. It handles various file formats and naming conventions
    commonly used for movies.

    Attributes:
        main_window (Optional[Any]): Reference to the main application window.
            Used to access the movie list widget and update the UI.
    """

    def __init__(self, parent: Optional[QWidget] = None) -> None:
        """
        Initialize the MovieSorterButton.

        Args:
            parent: Parent widget, typically the main window.
                   Controls the button's lifetime and placement in the UI.
        """
        super().__init__("Sort Movies", parent)
        self.clicked.connect(self.sort_movies)
        self.main_window: Optional[Any] = None
        self.setup_logging()
        logger.info("MovieSorterButton initialized")

    def setup_logging(self) -> None:
        """
        Configure logging for the movie sorter.
        We're using the module-level logger, so just log initialization.
        """
        logger.info("MovieSorterButton logging setup completed")

    def set_main_window(self, main_window: Any) -> None:
        """
        Set the reference to the main application window.

        Args:
            main_window: The main window instance containing the movie list widget.
                        Must have a 'movie_list' attribute for accessing the files to sort.
        """
        self.main_window = main_window

    @log_file_operation
    def sort_movies(self) -> None:
        """
        Handle the sort movies button click event.

        Processes all movies in the movie list and attempts to sort them into appropriate
        directories. Shows feedback to the user through message boxes.
        """
        if not self.main_window or not hasattr(self.main_window, 'movie_list'):
            error_msg = "Movie list not initialized"
            logger.error(error_msg)
            QMessageBox.warning(self, "Error", error_msg)
            return

        movie_list = self.main_window.movie_list
        if not movie_list.file_paths:
            logger.info("No movies to sort")
            QMessageBox.information(self, "No Movies", "No movies to sort")
            return

        try:
            # This is a placeholder - actual movie sorting would be implemented here
            # For now, we'll just show a message
            QMessageBox.information(self, "Not Implemented", "Movie sorting is not yet implemented")
        except Exception as e:
            error_msg = f"Error sorting movies: {str(e)}"
            logger.error(error_msg, exc_info=True)
            QMessageBox.critical(self, "Error", error_msg)

# Error type definitions for better error handling documentation
class MovieSortingError(Exception):
    """Base exception class for movie sorting errors."""
    pass

class MovieParsingError(MovieSortingError):
    """Raised when movie filename parsing fails."""
    pass

class MovieDuplicateError(MovieSortingError):
    """Raised when a duplicate movie is found."""
    pass

class MovieDirectoryError(MovieSortingError):
    """Raised when there are issues with movie directories."""
    pass

# Add error handling documentation to the module docstring
__doc__ = """
MediaSorter Movie Sorting Module

This module provides functionality for sorting movie files into organized directories.
It handles various movie file formats and naming conventions.

Error Handling:
    The module uses a hierarchy of custom exceptions for specific error cases:

    MovieSortingError
    ├── MovieParsingError
    │   - Raised when movie filename parsing fails
    │   - Examples: Invalid format, missing year, etc.
    ├── MovieDuplicateError
    │   - Raised when duplicate movies are found
    │   - Includes cases of different qualities/versions
    └── MovieDirectoryError
        - Raised for directory-related issues
        - Examples: Permission denied, disk full, etc.

    System exceptions are also handled:
    - FileNotFoundError: Missing files
    - PermissionError: Access denied
    - OSError: File system issues
"""