from PyQt5.QtWidgets import (
    QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QScrollArea, QDialog, QFrame, QLineEdit
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QIcon
import os
import json
from pathlib import Path

from .movie_grid import MovieGrid

class SidebarButton(QPushButton):
    """A custom button for the sidebar with hover and checked states."""
    
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setCheckable(True)
        self.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                color: rgba(255, 255, 255, 0.7);
                padding: 10px 20px;
                text-align: left;
                border-radius: 4px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.1);
                color: white;
            }
            QPushButton:checked {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                font-weight: bold;
            }
        """)

class Sidebar(QWidget):
    """A sidebar widget for movie library navigation."""
    
    category_changed = pyqtSignal(str)  # Emits the selected category
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Initialize the sidebar UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 20, 10, 20)
        layout.setSpacing(5)
        
        # Add library label
        library_label = QLabel("Library")
        library_label.setFont(QFont("Arial", 18, QFont.Bold))
        library_label.setStyleSheet("color: white;")
        layout.addWidget(library_label)
        layout.addSpacing(20)
        
        # Add category buttons
        self.buttons = {}
        categories = [
            "All Movies",
            "Recently Added",
            "Favorites",
            "Action",
            "Comedy",
            "Drama",
            "Horror",
            "Sci-Fi",
            "Thriller"
        ]
        
        for category in categories:
            btn = SidebarButton(category)
            btn.clicked.connect(lambda checked, c=category: self.on_category_clicked(c))
            layout.addWidget(btn)
            self.buttons[category] = btn
            
        # Set initial selection
        self.buttons["All Movies"].setChecked(True)
        
        layout.addStretch()
        
    def on_category_clicked(self, category):
        """Handle category button clicks."""
        # Uncheck all other buttons
        for btn in self.buttons.values():
            if btn != self.sender():
                btn.setChecked(False)
        
        self.category_changed.emit(category)

class MovieDetailsDialog(QDialog):
    """Dialog showing detailed movie information."""
    def __init__(self, movie_data, parent=None):
        super().__init__(parent)
        self.movie_data = movie_data
        self.setup_ui()
    
    def setup_ui(self):
        """Initialize the dialog UI."""
        self.setWindowTitle(self.movie_data['title'])
        self.setMinimumSize(800, 600)
        self.setStyleSheet("""
            QDialog {
                background: #2c3e50;
            }
            QLabel {
                color: white;
            }
            QPushButton {
                background: #8e44ad;
                border: none;
                border-radius: 4px;
                color: white;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background: #9b59b6;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # Create scroll area for content
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("QScrollArea { border: none; }")
        
        content = QWidget()
        content_layout = QVBoxLayout(content)
        
        # Add movie details
        title_label = QLabel(self.movie_data['title'])
        title_label.setFont(QFont("Arial", 24, QFont.Bold))
        content_layout.addWidget(title_label)
        
        # Add other movie information (year, genre, rating, etc.)
        if 'year' in self.movie_data:
            year_label = QLabel(f"Year: {self.movie_data['year']}")
            content_layout.addWidget(year_label)
        
        if 'genre' in self.movie_data:
            genre_label = QLabel(f"Genre: {self.movie_data['genre']}")
            content_layout.addWidget(genre_label)
        
        if 'rating' in self.movie_data:
            rating_label = QLabel(f"Rating: {self.movie_data['rating']}")
            content_layout.addWidget(rating_label)
        
        if 'overview' in self.movie_data:
            overview_label = QLabel(self.movie_data['overview'])
            overview_label.setWordWrap(True)
            content_layout.addWidget(overview_label)
        
        scroll.setWidget(content)
        layout.addWidget(scroll)
        
        # Add play button
        play_button = QPushButton("Play Movie")
        play_button.clicked.connect(self.play_movie)
        layout.addWidget(play_button)
    
    def play_movie(self):
        """Handle play button click."""
        # TODO: Implement movie playback
        pass

class MovieLibraryTab(QWidget):
    """The main movie library tab widget."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.api_key = None
        self.all_movies = []  # Initialize empty list for movies
        self.movies_folder = None
        self.setup_ui()
        self.load_movies()
        
    def set_api_key(self, api_key):
        """Set the TMDB API key."""
        self.api_key = api_key
        # Reload movies to fetch updated information
        self.load_movies()
        
    def set_movie_folder(self, folder_path):
        """Set the movies folder path and reload movies."""
        self.movies_folder = Path(folder_path)
        self.load_movies()
        
    def setup_ui(self):
        """Initialize the tab UI."""
        self.setStyleSheet("""
            QWidget {
                background: #141414;
            }
            QLineEdit {
                background: rgba(255, 255, 255, 0.1);
                border: none;
                border-radius: 4px;
                color: white;
                padding: 8px;
                font-size: 14px;
            }
            QLineEdit:focus {
                background: rgba(255, 255, 255, 0.15);
            }
        """)
        
        # Create main layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Add sidebar
        self.sidebar = Sidebar()
        self.sidebar.setFixedWidth(200)
        self.sidebar.category_changed.connect(self.on_category_changed)
        layout.addWidget(self.sidebar)
        
        # Create content area
        content = QWidget()
        content_layout = QVBoxLayout(content)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(20)
        
        # Add search bar
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search movies...")
        self.search_input.textChanged.connect(self.on_search_changed)
        search_layout.addWidget(self.search_input)
        
        content_layout.addLayout(search_layout)
        
        # Add movie grid
        self.movie_grid = MovieGrid()
        content_layout.addWidget(self.movie_grid)
        
        layout.addWidget(content)
        
    def load_movies(self):
        """Load movies from the configured movies folder."""
        if not self.movies_folder:
            # Try to get movies folder from settings
            try:
                settings_file = Path(__file__).parent.parent / "settings.json"
                if settings_file.exists():
                    with open(settings_file) as f:
                        settings = json.load(f)
                    self.movies_folder = Path(settings.get("movies_folder", ""))
            except Exception as e:
                print(f"Error loading settings: {e}")
                return
            
            if not self.movies_folder:
                return
            
        if not self.movies_folder.exists():
            return
            
        # Scan for movie files
        movies = []
        for file in self.movies_folder.rglob("*"):
            if file.suffix.lower() in [".mp4", ".mkv", ".avi"]:
                # Create movie data (this would normally include TMDB data)
                movie_data = {
                    "title": file.stem,
                    "file_path": str(file),
                    # Add placeholder data for testing
                    "year": 2024,
                    "overview": "Movie description would go here...",
                    "runtime": 120,
                    "rating": "PG-13",
                    "genres": ["Action", "Adventure"],
                }
                movies.append(movie_data)
                
        self.all_movies = movies
        self.filter_movies()
        
    def filter_movies(self):
        """Filter movies based on current search and category."""
        filtered = self.all_movies
        
        # Apply search filter
        search_text = self.search_input.text().lower()
        if search_text:
            filtered = [
                m for m in filtered
                if search_text in m["title"].lower()
            ]
            
        # Apply category filter
        category = next(
            (btn.text() for btn in self.sidebar.buttons.values() if btn.isChecked()),
            "All Movies"
        )
        
        if category != "All Movies":
            if category == "Recently Added":
                # Sort by added date and take first 20
                filtered = sorted(
                    filtered,
                    key=lambda m: os.path.getctime(m["file_path"]),
                    reverse=True
                )[:20]
            elif category == "Favorites":
                # Filter favorites (would need to implement favorites system)
                pass
            else:
                # Filter by genre
                filtered = [
                    m for m in filtered
                    if category in m.get("genres", [])
                ]
                
        self.movie_grid.load_movies(filtered)
        
    def on_category_changed(self, category):
        """Handle category changes."""
        self.filter_movies()
        
    def on_search_changed(self, text):
        """Handle search input changes."""
        self.filter_movies()

    def show_movie_details(self, movie_data):
        """Show the movie details dialog."""
        dialog = MovieDetailsDialog(movie_data, self)
        dialog.exec_()