import json
from pathlib import Path
from typing import Dict, List, Optional

class SearchConfig:
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or str(Path.home() / '.mediasorter' / 'search_config.json')
        self.default_config = {
            'excluded_directories': [
                '.git',
                'node_modules',
                '__pycache__',
                'venv',
                '.venv'
            ],
            'excluded_extensions': [
                '.pyc',
                '.pyo',
                '.pyd',
                '.git'
            ],
            'max_search_results': 1000,
            'cache_enabled': True,
            'cache_duration_hours': 24,
            'search_preferences': {
                'default_sort': 'name',
                'default_sort_reverse': False,
                'show_hidden_files': False,
                'fuzzy_search': True,
                'case_sensitive': False
            },
            'media_types': {
                'video': ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv'],
                'audio': ['.mp3', '.wav', '.flac', '.m4a', '.aac'],
                'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
                'document': ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.xlsx', '.pptx']
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict:
        """Load configuration from file or create default"""
        config_file = Path(self.config_path)
        
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    loaded_config = json.load(f)
                    # Merge with defaults to ensure all keys exist
                    return self._merge_configs(self.default_config, loaded_config)
            except (json.JSONDecodeError, OSError) as e:
                print(f"Error loading config: {e}")
                return self.default_config.copy()
        else:
            # Create default config file
            self.save_config(self.default_config)
            return self.default_config.copy()
    
    def _merge_configs(self, default: Dict, loaded: Dict) -> Dict:
        """Recursively merge loaded config with defaults"""
        merged = default.copy()
        
        for key, value in loaded.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                merged[key] = self._merge_configs(merged[key], value)
            else:
                merged[key] = value
                
        return merged
    
    def save_config(self, config: Dict) -> bool:
        """Save configuration to file"""
        config_file = Path(self.config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=4)
            self.config = config
            return True
        except OSError as e:
            print(f"Error saving config: {e}")
            return False
    
    def get_excluded_paths(self) -> List[str]:
        """Get list of excluded directory patterns"""
        return self.config['excluded_directories']
    
    def get_excluded_extensions(self) -> List[str]:
        """Get list of excluded file extensions"""
        return self.config['excluded_extensions']
    
    def get_media_extensions(self, media_type: str) -> List[str]:
        """Get list of file extensions for a media type"""
        return self.config['media_types'].get(media_type, [])
    
    def get_search_preference(self, key: str):
        """Get a specific search preference"""
        return self.config['search_preferences'].get(key)
    
    def set_search_preference(self, key: str, value) -> bool:
        """Set a search preference"""
        if key in self.config['search_preferences']:
            self.config['search_preferences'][key] = value
            return self.save_config(self.config)
        return False
    
    def add_excluded_directory(self, directory: str) -> bool:
        """Add a directory pattern to exclusion list"""
        if directory not in self.config['excluded_directories']:
            self.config['excluded_directories'].append(directory)
            return self.save_config(self.config)
        return True
    
    def remove_excluded_directory(self, directory: str) -> bool:
        """Remove a directory pattern from exclusion list"""
        if directory in self.config['excluded_directories']:
            self.config['excluded_directories'].remove(directory)
            return self.save_config(self.config)
        return True
    
    def add_media_extension(self, media_type: str, extension: str) -> bool:
        """Add a file extension to a media type"""
        if media_type in self.config['media_types']:
            if extension not in self.config['media_types'][media_type]:
                self.config['media_types'][media_type].append(extension)
                return self.save_config(self.config)
        return False
    
    def remove_media_extension(self, media_type: str, extension: str) -> bool:
        """Remove a file extension from a media type"""
        if media_type in self.config['media_types']:
            if extension in self.config['media_types'][media_type]:
                self.config['media_types'][media_type].remove(extension)
                return self.save_config(self.config)
        return False 