# health_checker.py v1.0
# System health monitoring module

from pathlib import Path
import os
import logging
from enum import Enum

class HealthStatus(Enum):
    GOOD = "GOOD"
    WARNING = "WARNING"
    ERROR = "ERROR"

class ComponentHealth:
    def __init__(self, name, status, message):
        self.name = name
        self.status = status
        self.message = message

def check_folder_health(folder_path, required=True):
    """Check if a folder exists and is writable"""
    try:
        path = Path(folder_path)
        if not path.exists():
            if required:
                return HealthStatus.ERROR, f"Folder does not exist: {folder_path}"
            else:
                return HealthStatus.WARNING, f"Optional folder not created yet: {folder_path}"

        # Try to write a test file
        test_file = path / ".health_check"
        try:
            test_file.write_text("test")
            test_file.unlink()  # Clean up
            return HealthStatus.GOOD, "Folder exists and is writable"
        except:
            return HealthStatus.ERROR, f"Folder is not writable: {folder_path}"
    except Exception as e:
        return HealthStatus.ERROR, f"Error checking folder {folder_path}: {e}"

def check_log_health(log_path):
    """Check if a log file exists and is writable"""
    try:
        path = Path(log_path)
        if not path.exists():
            return HealthStatus.WARNING, "Log file not created yet"

        # Check if file is writable
        try:
            with open(path, 'a') as f:
                f.write("")
            return HealthStatus.GOOD, "Log file is writable"
        except:
            return HealthStatus.ERROR, f"Log file is not writable: {log_path}"
    except Exception as e:
        return HealthStatus.ERROR, f"Error checking log {log_path}: {e}"

def check_system_health():
    """Check the health of all system components"""
    brain_folder = Path(__file__).resolve().parent.parent
    health_checks = []

    # Check Brain folder structure
    folders_to_check = {
        "Brain Folder": (brain_folder, True),
        "Settings Folder": (brain_folder / "settings", True),
        "Movie Code": (brain_folder / "movie_code", True),
        "TV Show Code": (brain_folder / "tv_show_code", True),
        "Status Code": (brain_folder / "status", True),
        "Movies Folder": (brain_folder / "Movies", False),
        "Sorting Folder": (brain_folder / "0.1 Sorting Folder", False),
        "Unsorted Folder": (brain_folder / "0.2 Unsorted Folder", False)
    }

    # Check each folder
    for name, (path, required) in folders_to_check.items():
        status, message = check_folder_health(path, required)
        health_checks.append(ComponentHealth(name, status, message))

    # Check log files
    logs_folder = brain_folder / "0.3 Brain Folder" / "logs"
    LOGS = {
        "Movie Sorter Log": logs_folder / "movie_sorter_log_3.txt",
        "File Sorter Log": logs_folder / "file_sorter_log_4.txt",
        "GUI Log": logs_folder / "gui_log_1.txt",
        "TV Show Log": logs_folder / "tv_show_log_3.txt",
        "Movie Handler Log": logs_folder / "movie_handler_log.txt"
    }

    # Check each log file
    for name, path in LOGS.items():
        status, message = check_log_health(path)
        health_checks.append(ComponentHealth(f"{name} File", status, message))

    # Check settings file
    settings_file = brain_folder / "settings" / "user_settings.json"
    try:
        if not settings_file.exists():
            health_checks.append(ComponentHealth(
                "Settings File",
                HealthStatus.WARNING,
                "Settings file not created yet"
            ))
        else:
            health_checks.append(ComponentHealth(
                "Settings File",
                HealthStatus.GOOD,
                "Settings file exists"
            ))
    except Exception as e:
        health_checks.append(ComponentHealth(
            "Settings File",
            HealthStatus.ERROR,
            f"Error checking settings file: {e}"
        ))

    return health_checks