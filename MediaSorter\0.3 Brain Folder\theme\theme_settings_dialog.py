from PyQt5.QtWidgets import (<PERSON><PERSON><PERSON>og, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QComboBox, QFrame, QGroupBox, QWidget)
from PyQt5.QtCore import Qt
from .theme_manager import Theme<PERSON><PERSON><PERSON>, Theme

class ThemeSettingsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = ThemeManager()
        self.setWindowTitle("Theme Settings")
        self.setMinimumWidth(400)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # Theme Selection
        theme_group = QGroupBox("Theme")
        theme_layout = QVBoxLayout()
        theme_group.setLayout(theme_layout)
        
        theme_selector = QHBoxLayout()
        theme_label = QLabel("Select Theme:")
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Light", "Dark"])
        self.theme_combo.setCurrentText("Dark" if self.theme_manager.current_theme == Theme.DARK else "Light")
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
        
        theme_selector.addWidget(theme_label)
        theme_selector.addWidget(self.theme_combo)
        theme_selector.addStretch()
        theme_layout.addLayout(theme_selector)
        
        # Preview Section
        preview_group = QGroupBox("Preview")
        preview_layout = QVBoxLayout()
        preview_group.setLayout(preview_layout)
        
        # Sample button for preview
        self.preview_button = QPushButton("Sample Button")
        preview_layout.addWidget(self.preview_button)
        
        # Add groups to main layout
        layout.addWidget(theme_group)
        layout.addWidget(preview_group)
        layout.addStretch()
        
        # Buttons
        button_layout = QHBoxLayout()
        apply_button = QPushButton("Apply")
        apply_button.clicked.connect(self.apply_theme)
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        
        button_layout.addStretch()
        button_layout.addWidget(apply_button)
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)
        
    def on_theme_changed(self, theme_name):
        """Preview the selected theme"""
        theme = Theme.DARK if theme_name == "Dark" else Theme.LIGHT
        self.theme_manager.apply_theme(self, theme)
        
    def apply_theme(self):
        """Apply the selected theme to the main application"""
        theme = Theme.DARK if self.theme_combo.currentText() == "Dark" else Theme.LIGHT
        self.theme_manager.apply_theme(self.parent(), theme)
        self.theme_manager.save_theme(theme) 