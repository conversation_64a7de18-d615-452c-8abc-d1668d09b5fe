import os
import subprocess
import json
from pathlib import Path
from .movie_player import MoviePlayer

class PlayerLauncher:
    """Utility class for launching different media players."""
    
    @staticmethod
    def load_settings():
        """Load player settings from file."""
        try:
            settings_dir = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) / "settings"
            settings_file = settings_dir / "player_settings.json"
            
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # Default settings
                return {
                    "player_type": "builtin",
                    "custom_player_path": ""
                }
        except Exception as e:
            print(f"Error loading player settings: {str(e)}")
            return {
                "player_type": "builtin",
                "custom_player_path": ""
            }
    
    @staticmethod
    def play_movie(file_path, parent=None):
        """Play a movie using the configured player."""
        settings = PlayerLauncher.load_settings()
        player_type = settings.get("player_type", "builtin")
        
        print(f"Playing movie: {file_path}")
        print(f"Using player type: {player_type}")
        
        if player_type == "builtin":
            # Use built-in player
            return PlayerLauncher.play_with_builtin(file_path, parent)
        elif player_type == "vlc":
            # Use VLC
            return PlayerLauncher.play_with_vlc(file_path)
        elif player_type == "custom":
            # Use custom player
            custom_path = settings.get("custom_player_path", "")
            return PlayerLauncher.play_with_custom(file_path, custom_path)
        else:
            # Fallback to built-in
            return PlayerLauncher.play_with_builtin(file_path, parent)
    
    @staticmethod
    def play_with_builtin(file_path, parent=None):
        """Play a movie with the built-in player."""
        try:
            player = MoviePlayer(parent)
            player.load_media(file_path)
            player.show()
            player.play()
            return player
        except Exception as e:
            print(f"Error playing with built-in player: {str(e)}")
            return None
    
    @staticmethod
    def play_with_vlc(file_path):
        """Play a movie with VLC."""
        try:
            # Try common VLC paths
            vlc_paths = [
                "C:\\Program Files\\VideoLAN\\VLC\\vlc.exe",
                "C:\\Program Files (x86)\\VideoLAN\\VLC\\vlc.exe",
                "vlc"  # If in PATH
            ]
            
            vlc_path = None
            for path in vlc_paths:
                if os.path.exists(path):
                    vlc_path = path
                    break
            
            if vlc_path:
                subprocess.Popen([vlc_path, file_path])
                return True
            else:
                print("VLC not found. Please install VLC or set a custom player.")
                return False
        except Exception as e:
            print(f"Error playing with VLC: {str(e)}")
            return False
    
    @staticmethod
    def play_with_custom(file_path, custom_path):
        """Play a movie with a custom player."""
        try:
            if os.path.exists(custom_path):
                subprocess.Popen([custom_path, file_path])
                return True
            else:
                print(f"Custom player not found at: {custom_path}")
                return False
        except Exception as e:
            print(f"Error playing with custom player: {str(e)}")
            return False
