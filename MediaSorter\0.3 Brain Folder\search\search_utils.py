import os
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional

def detect_media_type(file_path: str) -> str:
    """
    Detect the media type of a file based on its extension and naming pattern.
    Returns: 'video', 'audio', 'image', 'document', or 'other'
    """
    extensions = {
        'video': {'.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv'},
        'audio': {'.mp3', '.wav', '.flac', '.m4a', '.aac'},
        'image': {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'},
        'document': {'.pdf', '.doc', '.docx', '.txt', '.rtf', '.xlsx', '.pptx'}
    }
    
    ext = Path(file_path).suffix.lower()
    for media_type, ext_set in extensions.items():
        if ext in ext_set:
            return media_type
    return 'other'

def format_file_size(size_bytes: int) -> str:
    """Convert file size to human-readable format"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024
    return f"{size_bytes:.1f} TB"

def get_file_metadata(file_path: str) -> Dict:
    """Get comprehensive file metadata"""
    path = Path(file_path)
    stats = path.stat()
    
    return {
        'name': path.name,
        'extension': path.suffix.lower(),
        'size': stats.st_size,
        'size_formatted': format_file_size(stats.st_size),
        'created': datetime.fromtimestamp(stats.st_ctime),
        'modified': datetime.fromtimestamp(stats.st_mtime),
        'accessed': datetime.fromtimestamp(stats.st_atime),
        'media_type': detect_media_type(file_path),
        'is_hidden': path.name.startswith('.')
    }

def parse_date_range(date_str: str) -> tuple[Optional[datetime], Optional[datetime]]:
    """
    Parse date range strings in various formats.
    Supports: 'today', 'yesterday', 'last7days', 'last30days', 'thismonth', 'lastmonth'
    Or date ranges like: '2023-01-01:2023-12-31'
    """
    from datetime import datetime, timedelta
    
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    
    if not date_str or date_str.lower() == 'any':
        return None, None
        
    date_str = date_str.lower()
    
    if date_str == 'today':
        return today, today + timedelta(days=1)
    elif date_str == 'yesterday':
        return today - timedelta(days=1), today
    elif date_str == 'last7days':
        return today - timedelta(days=7), today + timedelta(days=1)
    elif date_str == 'last30days':
        return today - timedelta(days=30), today + timedelta(days=1)
    elif date_str == 'thismonth':
        return today.replace(day=1), (today.replace(day=1) + timedelta(days=32)).replace(day=1)
    elif date_str == 'lastmonth':
        last_month = (today.replace(day=1) - timedelta(days=1)).replace(day=1)
        return last_month, today.replace(day=1)
    
    # Try to parse explicit date range
    if ':' in date_str:
        start_str, end_str = date_str.split(':')
        try:
            start_date = datetime.strptime(start_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_str, '%Y-%m-%d') + timedelta(days=1)
            return start_date, end_date
        except ValueError:
            return None, None
            
    return None, None

def create_search_index(directory: str) -> Dict:
    """
    Create a simple search index for files in the given directory.
    Returns a dictionary mapping words to file paths.
    """
    index = {}
    for root, _, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            
            # Index the filename words
            words = file.lower().replace('.', ' ').split()
            for word in words:
                if word not in index:
                    index[word] = set()
                index[word].add(file_path)
                
    return index 