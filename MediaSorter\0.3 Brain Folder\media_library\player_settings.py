from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QRadioButton, QButtonGroup, QLineEdit,
    QFileDialog, QGroupBox
)
from PyQt5.QtCore import Qt
import os
import json
from pathlib import Path

class PlayerSettingsDialog(QDialog):
    """Dialog for configuring media player settings."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.settings = self.load_settings()
        self.setup_ui()
        
    def setup_ui(self):
        """Initialize the dialog UI."""
        self.setWindowTitle("Media Player Settings")
        self.setMinimumWidth(500)
        self.setStyleSheet("""
            QDialog {
                background-color: #1a1a1a;
                color: white;
            }
            QLabel, QRadioButton, QGroupBox {
                color: white;
            }
            QLineEdit {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #3d3d3d;
                border-radius: 4px;
                padding: 8px;
            }
            QPushButton {
                background-color: #0078d7;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1e90ff;
            }
            QGroupBox {
                border: 1px solid #3d3d3d;
                border-radius: 4px;
                margin-top: 1ex;
                padding-top: 1ex;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 3px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # Player selection group
        player_group = QGroupBox("Media Player")
        player_layout = QVBoxLayout(player_group)
        
        # Create radio buttons for player selection
        self.player_group = QButtonGroup(self)
        
        # Built-in player option
        self.builtin_radio = QRadioButton("Built-in Player")
        self.builtin_radio.setToolTip("Use the built-in Qt media player")
        self.player_group.addButton(self.builtin_radio, 1)
        player_layout.addWidget(self.builtin_radio)
        
        # VLC option
        self.vlc_radio = QRadioButton("VLC Media Player")
        self.vlc_radio.setToolTip("Use VLC for better compatibility with more formats")
        self.player_group.addButton(self.vlc_radio, 2)
        player_layout.addWidget(self.vlc_radio)
        
        # Custom player option
        self.custom_radio = QRadioButton("Custom External Player")
        self.custom_radio.setToolTip("Use a custom external media player")
        self.player_group.addButton(self.custom_radio, 3)
        player_layout.addWidget(self.custom_radio)
        
        # Custom player path
        custom_path_layout = QHBoxLayout()
        self.custom_path_input = QLineEdit(self.settings.get("custom_player_path", ""))
        self.custom_path_input.setPlaceholderText("Path to custom player executable")
        browse_button = QPushButton("Browse")
        browse_button.clicked.connect(self.browse_player)
        custom_path_layout.addWidget(self.custom_path_input)
        custom_path_layout.addWidget(browse_button)
        player_layout.addLayout(custom_path_layout)
        
        # Add player group to main layout
        layout.addWidget(player_group)
        
        # Set the current selection based on settings
        player_type = self.settings.get("player_type", "builtin")
        if player_type == "builtin":
            self.builtin_radio.setChecked(True)
        elif player_type == "vlc":
            self.vlc_radio.setChecked(True)
        elif player_type == "custom":
            self.custom_radio.setChecked(True)
        
        # Buttons
        button_layout = QHBoxLayout()
        save_button = QPushButton("Save")
        save_button.clicked.connect(self.save_settings)
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
    def browse_player(self):
        """Browse for a custom player executable."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Media Player", "", "Executable Files (*.exe);;All Files (*.*)"
        )
        if file_path:
            self.custom_path_input.setText(file_path)
            self.custom_radio.setChecked(True)
            
    def save_settings(self):
        """Save the player settings."""
        if self.builtin_radio.isChecked():
            player_type = "builtin"
        elif self.vlc_radio.isChecked():
            player_type = "vlc"
        elif self.custom_radio.isChecked():
            player_type = "custom"
        else:
            player_type = "builtin"  # Default
            
        self.settings["player_type"] = player_type
        self.settings["custom_player_path"] = self.custom_path_input.text()
        
        # Save to file
        self.save_settings_to_file()
        self.accept()
        
    def load_settings(self):
        """Load player settings from file."""
        try:
            settings_dir = self.get_settings_dir()
            settings_file = settings_dir / "player_settings.json"
            
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # Default settings
                return {
                    "player_type": "builtin",
                    "custom_player_path": ""
                }
        except Exception as e:
            print(f"Error loading player settings: {str(e)}")
            return {
                "player_type": "builtin",
                "custom_player_path": ""
            }
            
    def save_settings_to_file(self):
        """Save player settings to file."""
        try:
            settings_dir = self.get_settings_dir()
            settings_dir.mkdir(parents=True, exist_ok=True)
            
            settings_file = settings_dir / "player_settings.json"
            
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=4)
                
            print(f"Player settings saved to {settings_file}")
        except Exception as e:
            print(f"Error saving player settings: {str(e)}")
            
    def get_settings_dir(self):
        """Get the settings directory path."""
        # Use the same location as other settings
        return Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) / "settings"
